// User types
export interface User {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  email: string;
  emailVerification: boolean;
  status: boolean;
  labels: string[];
  prefs: Record<string, any>;
}

// Authentication types
export interface AuthUser {
  $id: string;
  name: string;
  email: string;
  avatar?: string;
  avatarUrl?: string;
  discordId?: string;
  isAdmin: boolean;
  totalImagesLimit: number;
  credits: number;
  currentImages?: number;
}

// User configuration defaults
export const DEFAULT_USER_CONFIG = {
  isAdmin: false, // Regular users are NOT admins by default
  totalImagesLimit: 100,
  credits: 200.00,
} as const;

// Image types
export interface ImageUpload {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  title: string;
  description?: string;
  fileId: string;
  fileUrl: string;
  userId: string;
  userName: string;
  tags: string[];
  isPublic: boolean;
  views: number;
  likes: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Form types
export interface FormState {
  isLoading: boolean;
  error: string | null;
  success: boolean;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
}

// Dashboard types
export interface DashboardStats {
  totalImages: number;
  totalViews: number;
  totalLikes: number;
  storageUsed: number;
}

// User statistics and configuration
export interface UserStats {
  currentImages: number;
  totalImagesLimit: number;
  credits: number;
  storageUsed: number;
  storageLimit: number;
}

export interface UserConfiguration {
  isAdmin: boolean;
  totalImagesLimit: number;
  credits: number;
  theme?: 'light' | 'dark' | 'auto';
  notifications?: boolean;
  embedTitle?: string;
  embedFooter?: string;
  embedColor?: string;
  lastDailyCreditsClaimDate?: string;
}

// Embed settings types
export interface EmbedSettings {
  embedTitle: string;
  embedFooter: string;
  embedColor: string;
}

// Uploaded image types
export interface UploadedImage {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  filename: string;
  fileId: string;
  mimeType: string;
  fileSize: number;
  uniqueId: string;
  uploadDate: string;
  isPublic: boolean;
}

// Component props types
export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface InputProps {
  label?: string;
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// Store System Types
export interface StorePlan {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  name: string;
  description: string;
  imageStock: number;
  creditCost: number;
  isActive: boolean;
  createdBy: string;
  features?: string[];
  popularTag?: boolean;
}

export interface CartItem {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  planId: string;
  quantity: number;
  addedAt: string;
  plan?: StorePlan; // Populated when fetching cart
}

export interface Order {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  orderNumber: string;
  items: OrderItem[];
  totalCredits: number;
  total: number; // Same as totalCredits, for convenience
  discountAmount: number;
  couponCode?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  purchaseDate: string;
  emailSent: boolean;
}

export interface OrderItem {
  planId: string;
  planName: string;
  imageStock: number;
  creditCost: number;
  quantity: number;
}

export interface Coupon {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  code: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  maxDiscountAmount?: number;
  minimumAmount?: number;
  usageLimit?: number;
  usedCount: number;
  singleUsePerUser: boolean;
  isActive: boolean;
  expiresAt: string;
  createdBy: string;
  description?: string;
}

export interface CreditTransaction {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  userId: string;
  type: 'purchase' | 'refund' | 'bonus' | 'daily_earn' | 'admin_credit' | 'manual_adjustment';
  amount: number;
  description: string;
  orderId?: string;
  balanceAfter: number;
  transactionDate: string;
  ipAddress?: string;
}

export interface AdminLog {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  adminUserId: string;
  action: string;
  targetType: 'user' | 'plan' | 'order' | 'coupon' | 'system';
  targetId?: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
}

// Admin Dashboard Types
export interface AdminStats {
  totalUsers: number;
  totalRevenue: number;
  totalOrders: number;
  totalImages: number;
  activeUsers: number;
  revenueThisMonth: number;
  ordersThisMonth: number;
  newUsersThisMonth: number;
}

export interface UserManagement {
  user: AuthUser;
  stats: UserStats;
  transactions: CreditTransaction[];
  orders: Order[];
}

// Cart and Checkout Types
export interface CartSummary {
  items: CartItem[];
  totalItems: number;
  totalCredits: number;
  totalImageStock: number;
  subtotal: number;
  discount: number;
  total: number;
  couponCode?: string;
  couponDetails?: Coupon | null;
}

export interface CheckoutSummary {
  cartItems: CartItem[];
  subtotal: number;
  discount: number;
  total: number;
  couponCode?: string;
  totalImageStock: number;
}

export interface CheckoutData {
  userId: string;
  paymentMethod: 'credits';
  couponCode?: string;
}

// Enhanced User Configuration
export interface EnhancedUserConfiguration extends UserConfiguration {
  role?: 'user' | 'admin' | 'super_admin';
  lastLogin?: string;
  accountStatus?: 'active' | 'suspended' | 'pending';
}
