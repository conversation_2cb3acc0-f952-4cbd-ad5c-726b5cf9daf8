{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "appwrite": "^18.1.1", "clsx": "^2.1.1", "next": "15.3.4", "node-appwrite": "^17.0.0", "nodemailer": "^7.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "9.30.1", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}