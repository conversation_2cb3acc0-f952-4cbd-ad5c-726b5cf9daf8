# AveImgCloud Admin Store System - Database Schema

## Overview
This document outlines the complete Appwrite database schema required for the Admin Store System implementation.

## Collections Required

### 1. **store_plans** Collection
**Purpose**: Store subscription plans that users can purchase

**Attributes**:
- `name` (String, Required, Size: 1-100) - Plan display name
- `description` (String, Required, Size: 1-500) - Plan benefits description  
- `imageStock` (Integer, Required, Range: 1-10000) - Number of additional image uploads
- `creditCost` (Integer, Required, Range: 1-100000) - Price in credits
- `isActive` (<PERSON><PERSON><PERSON>, Required, Default: true) - Whether plan is available for purchase
- `createdBy` (String, Required, Size: 36) - Admin user ID who created the plan
- `features` (String, Array, Optional) - List of plan features for display
- `popularTag` (<PERSON><PERSON><PERSON>, Default: false) - Mark as "Popular" plan

**Indexes**:
- `isActive` (ASC) - For filtering active plans
- `creditCost` (ASC) - For sorting by price
- `createdAt` (DESC) - For admin listing

**Permissions**:
- Create: Admin users only
- Read: All authenticated users
- Update: Admin users only  
- Delete: Admin users only

### 2. **shopping_cart** Collection
**Purpose**: Store user shopping cart items

**Attributes**:
- `userId` (String, Required, Size: 36) - User ID who owns the cart
- `planId` (String, Required, Size: 36) - Reference to store_plans collection
- `quantity` (Integer, Required, Default: 1) - Number of plans (always 1 for now)
- `addedAt` (DateTime, Required) - When item was added to cart

**Indexes**:
- `userId` (ASC) - For fetching user's cart
- `planId` (ASC) - For plan lookups
- `addedAt` (DESC) - For ordering cart items

**Permissions**:
- Create: User (own documents only)
- Read: User (own documents only)
- Update: User (own documents only)
- Delete: User (own documents only)

### 3. **orders** Collection  
**Purpose**: Store completed purchase orders

**Attributes**:
- `userId` (String, Required, Size: 36) - User who made the purchase
- `orderNumber` (String, Required, Size: 20) - Unique order identifier
- `items` (String, Required) - JSON stringified array of purchased items
- `totalCredits` (Integer, Required) - Total credits spent
- `discountAmount` (Integer, Default: 0) - Credits saved from coupons
- `couponCode` (String, Optional, Size: 50) - Applied coupon code
- `status` (String, Required, Default: "completed") - Order status
- `purchaseDate` (DateTime, Required) - When order was completed
- `emailSent` (Boolean, Default: false) - Confirmation email status

**Indexes**:
- `userId` (ASC) - For user order history
- `orderNumber` (UNIQUE) - For order lookups
- `purchaseDate` (DESC) - For sorting orders
- `status` (ASC) - For filtering by status

**Permissions**:
- Create: User (own documents only), Admin (all documents)
- Read: User (own documents only), Admin (all documents)
- Update: Admin only
- Delete: Admin only

### 4. **coupons** Collection
**Purpose**: Store discount coupon codes

**Attributes**:
- `code` (String, Required, Unique, Size: 3-50) - Coupon code
- `discountType` (String, Required) - "percentage" or "fixed"
- `discountValue` (Integer, Required) - Discount amount (% or credits)
- `minPurchase` (Integer, Default: 0) - Minimum purchase amount required
- `maxUses` (Integer, Default: 0) - Maximum number of uses (0 = unlimited)
- `currentUses` (Integer, Default: 0) - Current usage count
- `isActive` (Boolean, Default: true) - Whether coupon is active
- `expiryDate` (DateTime, Optional) - When coupon expires
- `createdBy` (String, Required, Size: 36) - Admin who created the coupon
- `description` (String, Optional, Size: 200) - Internal description

**Indexes**:
- `code` (UNIQUE) - For coupon lookups
- `isActive` (ASC) - For filtering active coupons  
- `expiryDate` (ASC) - For expiry checks
- `createdAt` (DESC) - For admin listing

**Permissions**:
- Create: Admin only
- Read: All authenticated users (for validation)
- Update: Admin only
- Delete: Admin only

### 5. **credit_transactions** Collection
**Purpose**: Log all credit transactions for audit trail

**Attributes**:
- `userId` (String, Required, Size: 36) - User involved in transaction
- `type` (String, Required) - "purchase", "refund", "admin_credit", "plan_purchase"
- `amount` (Integer, Required) - Credits added/deducted (positive/negative)
- `reason` (String, Required, Size: 200) - Transaction description
- `relatedOrderId` (String, Optional, Size: 36) - Related order if applicable
- `adminUserId` (String, Optional, Size: 36) - Admin who performed action
- `balanceBefore` (Integer, Required) - User's credit balance before transaction
- `balanceAfter` (Integer, Required) - User's credit balance after transaction
- `metadata` (String, Optional) - Additional JSON data

**Indexes**:
- `userId` (ASC) - For user transaction history
- `type` (ASC) - For filtering by transaction type
- `createdAt` (DESC) - For chronological ordering
- `adminUserId` (ASC) - For admin activity tracking

**Permissions**:
- Create: Admin only, System (via server functions)
- Read: User (own documents only), Admin (all documents)
- Update: None (immutable audit log)
- Delete: Admin only (rare cases)

### 6. **admin_logs** Collection
**Purpose**: Log admin actions for security audit

**Attributes**:
- `adminUserId` (String, Required, Size: 36) - Admin who performed action
- `action` (String, Required, Size: 100) - Action performed
- `targetType` (String, Required) - "user", "plan", "order", "coupon", "system"
- `targetId` (String, Optional, Size: 36) - ID of affected resource
- `details` (String, Optional, Size: 1000) - Additional action details
- `ipAddress` (String, Optional, Size: 45) - Admin's IP address
- `userAgent` (String, Optional, Size: 500) - Admin's browser info
- `success` (Boolean, Required) - Whether action succeeded

**Indexes**:
- `adminUserId` (ASC) - For admin activity tracking
- `action` (ASC) - For filtering by action type
- `targetType` (ASC) - For filtering by target type  
- `createdAt` (DESC) - For chronological ordering
- `success` (ASC) - For filtering failed actions

**Permissions**:
- Create: Admin only, System (via server functions)
- Read: Admin only
- Update: None (immutable audit log)
- Delete: Admin only (with restrictions)

## Existing Collections (Enhanced)

### **user_profiles** Collection (Enhanced)
**Existing Attributes Used**:
- `isAdmin` (Boolean, Default: false) - Admin access flag (already exists)

**New Attributes to Add**:
- `lastLogin` (DateTime, Optional) - Last login timestamp
- `accountStatus` (String, Default: "active") - "active", "suspended", "pending"

## Database Rules & Constraints

### Business Rules
1. **Plans**: Only active plans can be purchased
2. **Cart**: Users can only have one instance of each plan in cart
3. **Orders**: Order numbers must be unique and sequential
4. **Coupons**: Cannot be used if expired or usage limit reached
5. **Credits**: Must be positive, transactions must balance
6. **Admin Actions**: All admin actions must be logged

### Data Validation
1. **Email formats**: Validated via Appwrite built-in validation
2. **Credit amounts**: Must be integers (stored in smallest unit)
3. **Dates**: All dates in ISO 8601 format
4. **JSON fields**: Must be valid JSON strings

### Performance Considerations
1. **Indexes**: All frequently queried fields are indexed
2. **Pagination**: Large result sets use cursor-based pagination
3. **Caching**: Cart and user stats can be cached client-side
4. **Background Jobs**: Email sending and cleanup via Appwrite Functions

## Security Considerations

### Authentication
- All collections require authentication
- Admin actions require admin role verification
- Session-based access control

### Authorization  
- Users can only access their own cart, orders, transactions
- Admins can access all data with full CRUD permissions
- Audit logs track all administrative actions

### Data Protection
- PII is minimal and encrypted at rest
- Credit card info is never stored (credits only)
- Audit trails are immutable once created

## Sample Appwrite CLI Commands

```bash
# Create store_plans collection
appwrite databases createCollection --databaseId [DB_ID] --collectionId store_plans --name "Store Plans"

# Create attributes for store_plans
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId store_plans --key name --size 100 --required true
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId store_plans --key description --size 500 --required true
appwrite databases createIntegerAttribute --databaseId [DB_ID] --collectionId store_plans --key imageStock --required true --min 1 --max 10000
appwrite databases createIntegerAttribute --databaseId [DB_ID] --collectionId store_plans --key creditCost --required true --min 1 --max 100000
appwrite databases createBooleanAttribute --databaseId [DB_ID] --collectionId store_plans --key isActive --required true --default true
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId store_plans --key createdBy --size 36 --required true

# Create shopping_cart collection  
appwrite databases createCollection --databaseId [DB_ID] --collectionId shopping_cart --name "Shopping Cart"

# ... (repeat for all collections)
```

## Migration Notes

1. **Existing Data**: Current user data remains unchanged
2. **Admin Users**: Set `isAdmin` to `true` and `role` to `"admin"` for admin users
3. **Initial Plans**: Create sample plans via admin interface after implementation
4. **Credit System**: Existing user credits continue to work normally

This schema provides a robust foundation for the admin store system with proper audit trails, security, and scalability.
