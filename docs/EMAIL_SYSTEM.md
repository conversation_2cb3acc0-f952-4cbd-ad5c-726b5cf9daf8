# AveImgCloud Email System Documentation

## Overview

The AveImgCloud email system provides comprehensive email functionality with SMTP support, multiple email templates, and integration throughout the application. The system supports both development (simulated) and production (SMTP) modes.

## Features

- ✅ **SMTP Configuration**: Full SMTP support with popular email providers
- ✅ **Email Templates**: Professional HTML templates with dark theme styling
- ✅ **Multiple Email Types**: Welcome, order confirmation, credit transactions, admin alerts
- ✅ **Error Handling**: Comprehensive error handling with logging integration
- ✅ **Test Mode**: Development-friendly simulated email sending
- ✅ **Batch Sending**: Support for sending multiple emails efficiently
- ✅ **Service Status**: Real-time monitoring of email service configuration

## Email Types

### 1. Welcome Emails
- **Trigger**: New Discord OAuth user registration
- **Content**: Welcome message, account stats, getting started information
- **Integration**: Automatically sent during user creation in `userService.ensureUserExists()`

### 2. Order Confirmation Emails
- **Trigger**: Successful store purchase
- **Content**: Order details, purchased items, total cost, next steps
- **Integration**: Sent during checkout process in `storeService.processCheckout()`

### 3. Credit Transaction Notifications
- **Trigger**: Credit earnings, spending, or bonus credits
- **Content**: Transaction details, amount, new balance, transaction history link
- **Integration**: Sent during daily credits claim and store purchases

### 4. Admin Alert Emails
- **Trigger**: System errors, warnings, or critical events
- **Content**: Alert type, message, technical details, timestamp
- **Integration**: Available for system monitoring and error notifications

## Configuration

### Environment Variables

Add these variables to your `.env.local` file:

```env
# SMTP Configuration (Required for production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here

# Email Sender Information
SMTP_FROM_NAME=AveImgCloud
SMTP_FROM_EMAIL=<EMAIL>
```

### Popular SMTP Providers

| Provider | SMTP Host | Port | Security |
|----------|-----------|------|----------|
| Gmail | smtp.gmail.com | 587 | STARTTLS |
| Outlook | smtp-mail.outlook.com | 587 | STARTTLS |
| Yahoo | smtp.mail.yahoo.com | 587 | STARTTLS |
| SendGrid | smtp.sendgrid.net | 587 | STARTTLS |
| Mailgun | smtp.mailgun.org | 587 | STARTTLS |

## API Endpoints

### GET /api/email
Get email service status and configuration information.

**Response:**
```json
{
  "message": "Email API is running",
  "status": {
    "configured": true,
    "testMode": false
  },
  "endpoints": {...},
  "supportedEmailTypes": [...]
}
```

### POST /api/email/send
Send a custom email with specified content.

**Request:**
```json
{
  "to": "<EMAIL>",
  "subject": "Email Subject",
  "html": "<h1>Email Content</h1>",
  "emailType": "custom",
  "from": "<EMAIL>",
  "replyTo": "<EMAIL>"
}
```

### POST /api/email/test
Test email functionality with predefined templates.

**Request:**
```json
{
  "type": "welcome",
  "recipient": "<EMAIL>"
}
```

**Supported Test Types:**
- `welcome` - Welcome email for new users
- `order_confirmation` - Purchase confirmation email
- `credit_transaction` - Credit transaction notification
- `admin_alert` - Admin alert email

## Integration Examples

### Sending Welcome Email
```typescript
import { emailService } from '@/lib/services/emailService';

const user: AuthUser = {
  $id: 'user-id',
  name: 'John Doe',
  email: '<EMAIL>',
  // ... other properties
};

const result = await emailService.sendWelcomeEmail(user);
if (result.success) {
  console.log('Welcome email sent successfully');
}
```

### Sending Order Confirmation
```typescript
const result = await emailService.sendPurchaseConfirmation(
  userEmail,
  userName,
  order
);
```

### Sending Credit Transaction Notification
```typescript
const result = await emailService.sendCreditTransactionNotification(
  userEmail,
  userName,
  'earned', // 'earned' | 'spent' | 'bonus'
  30.00,
  'Daily credits claim',
  230.00 // new balance
);
```

## Development vs Production

### Development Mode (Test Mode)
- **Activation**: Automatically enabled when SMTP credentials are not configured
- **Behavior**: Simulates email sending without actual delivery
- **Logging**: All email attempts are logged to console
- **Message IDs**: Generated with `sim_` prefix for identification

### Production Mode (SMTP Mode)
- **Activation**: Enabled when valid SMTP credentials are provided
- **Behavior**: Sends actual emails via configured SMTP server
- **Logging**: Comprehensive logging with success/failure tracking
- **Message IDs**: Real message IDs from SMTP server

## Error Handling

The email system includes comprehensive error handling:

- **SMTP Connection Errors**: Graceful fallback to test mode
- **Authentication Failures**: Clear error messages and logging
- **Template Generation Errors**: Fallback to basic templates
- **Rate Limiting**: Built-in protection against spam
- **Logging Integration**: All events logged via `loggingService`

## Testing

Use the test endpoint to verify email functionality:

```bash
# Test welcome email
curl -X POST http://localhost:3001/api/email/test \
  -H "Content-Type: application/json" \
  -d '{"type": "welcome", "recipient": "<EMAIL>"}'

# Test order confirmation
curl -X POST http://localhost:3001/api/email/test \
  -H "Content-Type: application/json" \
  -d '{"type": "order_confirmation", "recipient": "<EMAIL>"}'
```

## Monitoring

Monitor email service health:

```typescript
const status = emailService.getServiceStatus();
console.log('Email service configured:', status.configured);
console.log('Test mode active:', status.testMode);
```

## Troubleshooting

### Common Issues

1. **SMTP Authentication Failed**
   - Verify SMTP credentials
   - Check if app passwords are required (Gmail)
   - Ensure less secure app access is enabled

2. **Emails Not Sending**
   - Check SMTP configuration
   - Verify network connectivity
   - Review error logs in console

3. **Template Rendering Issues**
   - Check for missing user data
   - Verify template variables
   - Review HTML syntax

### Debug Mode

Enable detailed logging by checking the browser console and server logs for email-related messages prefixed with `✅` (success) or `❌` (error).
