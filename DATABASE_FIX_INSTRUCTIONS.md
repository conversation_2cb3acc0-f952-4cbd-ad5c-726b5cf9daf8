# AveImgCloud Database Schema Fix

## **Issue Summary**
The error "Invalid document structure: Unknown attribute: 'lastDailyCreditsClaimDate'" occurs because several attributes defined in the TypeScript interfaces are missing from the Appwrite `user_profiles` collection.

## **Root Cause**
The `UserConfiguration` interface in `types/index.ts` defines attributes that don't exist in the Appwrite database:

```typescript
export interface UserConfiguration {
  isAdmin: boolean;
  totalImagesLimit: number;
  credits: number;
  theme?: 'light' | 'dark' | 'auto';
  notifications?: boolean;
  embedTitle?: string;
  embedFooter?: string;
  embedColor?: string;
  lastDailyCreditsClaimDate?: string; // ← This attribute is missing!
}
```

## **Missing Attributes in user_profiles Collection**

| Attribute | Type | Required | Default | Purpose |
|-----------|------|----------|---------|---------|
| `lastDailyCreditsClaimDate` | String | No | - | Tracks when user last claimed daily credits |
| `theme` | String | No | "dark" | User's preferred theme |
| `notifications` | Boolean | No | true | Notification preferences |
| `embedTitle` | String | No | - | Custom embed title for images |
| `embedFooter` | String | No | - | Custom embed footer for images |
| `embedColor` | String | No | - | Custom embed color (hex format) |
| `role` | String | No | "user" | User role (from EnhancedUserConfiguration) |
| `lastLogin` | String | No | - | Last login timestamp |
| `accountStatus` | String | No | "active" | Account status |

## **SOLUTION: Add Missing Attributes to Appwrite**

### **Step 1: Access Appwrite Console**
1. Go to your Appwrite Console: `https://api.avehubs.com/v1` (or your Appwrite URL)
2. Navigate to **Databases** → Your Database → **user_profiles** collection

### **Step 2: Add Missing Attributes**

**Option A: Using Appwrite Console (Recommended)**
1. Click on **user_profiles** collection
2. Go to **Attributes** tab
3. Click **Create Attribute** for each missing attribute:

**Daily Credits Tracking:**
- **Attribute Key:** `lastDailyCreditsClaimDate`
- **Type:** String
- **Size:** 30
- **Required:** No
- **Default:** (leave empty)

**User Preferences:**
- **Attribute Key:** `theme`
- **Type:** String  
- **Size:** 10
- **Required:** No
- **Default:** `dark`

- **Attribute Key:** `notifications`
- **Type:** Boolean
- **Required:** No
- **Default:** `true`

**Embed Customization:**
- **Attribute Key:** `embedTitle`
- **Type:** String
- **Size:** 100
- **Required:** No

- **Attribute Key:** `embedFooter`
- **Type:** String
- **Size:** 100
- **Required:** No

- **Attribute Key:** `embedColor`
- **Type:** String
- **Size:** 7
- **Required:** No

**Enhanced User Management:**
- **Attribute Key:** `role`
- **Type:** String
- **Size:** 20
- **Required:** No
- **Default:** `user`

- **Attribute Key:** `lastLogin`
- **Type:** String
- **Size:** 30
- **Required:** No

- **Attribute Key:** `accountStatus`
- **Type:** String
- **Size:** 20
- **Required:** No
- **Default:** `active`

**Option B: Using Appwrite CLI**
```bash
# Replace [DB_ID] with your actual database ID

# Daily credits tracking
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key lastDailyCreditsClaimDate --size 30 --required false

# User preferences
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key theme --size 10 --required false --default "dark"
appwrite databases createBooleanAttribute --databaseId [DB_ID] --collectionId user_profiles --key notifications --required false --default true

# Embed customization
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key embedTitle --size 100 --required false
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key embedFooter --size 100 --required false
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key embedColor --size 7 --required false

# Enhanced user management
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key role --size 20 --required false --default "user"
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key lastLogin --size 30 --required false
appwrite databases createStringAttribute --databaseId [DB_ID] --collectionId user_profiles --key accountStatus --size 20 --required false --default "active"
```

### **Step 3: Verify Fix**
1. After adding all attributes, test the daily credits functionality
2. Go to your dashboard and try claiming daily credits
3. Check that user settings can be saved properly

## **Code Changes Made**
✅ **Updated userService.ts** - Added `lastDailyCreditsClaimDate` handling in all configuration methods
✅ **Updated database-schema.md** - Documented all missing attributes
✅ **Created this fix guide** - Step-by-step instructions

## **What This Fixes**
- ✅ Daily credits claiming functionality
- ✅ User theme preferences
- ✅ Notification settings
- ✅ Custom embed settings for images
- ✅ Enhanced user management features
- ✅ Prevents "Unknown attribute" database errors

## **Next Steps After Database Fix**
1. Test daily credits claiming
2. Test user settings page
3. Verify admin user management works
4. Add Admin Logs sidebar functionality
5. Implement email confirmation system

## **Important Notes**
- All new attributes are **optional** to maintain backward compatibility
- Existing users will get default values automatically
- The fix is non-breaking and safe to apply to production
- Make sure to backup your database before making changes
