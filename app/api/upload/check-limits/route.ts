import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/lib/appwrite/auth';
import { userService } from '@/lib/services/userService';
import { emailService } from '@/lib/services/emailService';
import { loggingService } from '@/lib/services/loggingService';

// POST /api/upload/check-limits - Check image limits and send warnings if needed
export async function POST(_request: NextRequest) {
  try {
    // Get current user
    const user = await authService.getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Get current user stats
    const stats = await userService.getUserStats(user.$id);
    const percentage = Math.round((stats.currentImages / stats.totalImagesLimit) * 100);
    
    // Determine if we need to send warnings
    let shouldSendWarning = false;
    let warningType: '80%' | '95%' | '100%' = '80%';
    
    if (percentage >= 100) {
      warningType = '100%';
      shouldSendWarning = true;
    } else if (percentage >= 95) {
      warningType = '95%';
      shouldSendWarning = true;
    } else if (percentage >= 80) {
      warningType = '80%';
      shouldSendWarning = true;
    }
    
    if (shouldSendWarning) {
      try {
        // Send warning email
        const emailResult = await emailService.sendImageLimitWarning(user, {
          currentCount: stats.currentImages,
          totalLimit: stats.totalImagesLimit,
          percentage,
          warningType
        });

        // Log the warning event
        await loggingService.logSystemEvent({
          type: 'warning',
          action: 'image_limit_warning',
          details: `Image limit warning sent: ${warningType} usage (${stats.currentImages}/${stats.totalImagesLimit})`,
          metadata: {
            userId: user.$id,
            userEmail: user.email,
            warningType,
            currentImages: stats.currentImages,
            totalLimit: stats.totalImagesLimit,
            percentage,
            emailSent: emailResult.success
          },
          severity: warningType === '100%' ? 'high' : warningType === '95%' ? 'medium' : 'low'
        });

        return NextResponse.json({
          success: true,
          warningSent: true,
          warningType,
          percentage,
          message: `Image limit warning sent: ${warningType} usage`
        });
      } catch (emailError) {
        console.error('Failed to send image limit warning:', emailError);
        return NextResponse.json({
          success: false,
          error: 'Failed to send warning email'
        }, { status: 500 });
      }
    }

    return NextResponse.json({
      success: true,
      warningSent: false,
      percentage,
      message: 'No warning needed'
    });

  } catch (error) {
    console.error('Image limit check error:', error);
    return NextResponse.json(
      { error: 'Failed to check image limits' },
      { status: 500 }
    );
  }
}

// GET /api/upload/check-limits - Get current usage info
export async function GET() {
  try {
    const user = await authService.getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const stats = await userService.getUserStats(user.$id);
    const percentage = Math.round((stats.currentImages / stats.totalImagesLimit) * 100);
    
    return NextResponse.json({
      currentImages: stats.currentImages,
      totalLimit: stats.totalImagesLimit,
      percentage,
      remaining: stats.totalImagesLimit - stats.currentImages,
      canUpload: stats.currentImages < stats.totalImagesLimit
    });

  } catch (error) {
    console.error('Get usage info error:', error);
    return NextResponse.json(
      { error: 'Failed to get usage info' },
      { status: 500 }
    );
  }
}
