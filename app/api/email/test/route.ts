import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/services/emailService';
import { AuthUser, Order } from '@/types';

// POST /api/email/test - Test email functionality
export async function POST(request: NextRequest) {
  try {
    const { type, recipient } = await request.json();
    
    if (!recipient) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    let result;
    
    switch (type) {
      case 'welcome':
        const testUser: AuthUser = {
          $id: 'test-user-id',
          name: 'Test User',
          email: recipient,
          isAdmin: true,
          totalImagesLimit: 100,
          credits: 200.00,
          avatarUrl: undefined,
          discordId: undefined
        };
        result = await emailService.sendWelcomeEmail(testUser);
        break;
        
      case 'order_confirmation':
        const testOrder: Order = {
          $id: 'test-order-id',
          $createdAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString(),
          userId: 'test-user-id',
          orderNumber: 'TEST-' + Date.now(),
          items: [
            {
              planId: 'test-plan-1',
              planName: 'Starter Plan',
              imageStock: 50,
              creditCost: 25,
              quantity: 1
            },
            {
              planId: 'test-plan-2',
              planName: 'Pro Plan',
              imageStock: 200,
              creditCost: 75,
              quantity: 1
            }
          ],
          totalCredits: 100,
          total: 100,
          discountAmount: 0,
          couponCode: undefined,
          status: 'completed',
          purchaseDate: new Date().toISOString(),
          emailSent: false
        };
        result = await emailService.sendPurchaseConfirmation(
          recipient,
          'Test User',
          testOrder
        );
        break;
        
      case 'credit_transaction':
        result = await emailService.sendCreditTransactionNotification(
          recipient,
          'Test User',
          'earned',
          30.00,
          'Daily credits claim',
          230.00
        );
        break;
        
      case 'admin_alert':
        result = await emailService.sendAdminAlert(
          recipient,
          'warning',
          'Test Admin Alert',
          'This is a test admin alert to verify email functionality.',
          {
            testMode: true,
            timestamp: new Date().toISOString(),
            systemStatus: 'operational'
          }
        );
        break;

      case 'login_notification':
        const loginUser: AuthUser = {
          $id: 'test-user-id',
          name: 'Test User',
          email: recipient,
          isAdmin: false,
          totalImagesLimit: 100,
          credits: 200.00,
          avatarUrl: undefined,
          discordId: undefined
        };
        result = await emailService.sendLoginNotification(loginUser, {
          timestamp: new Date().toISOString(),
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          location: 'New York, NY, USA',
          isNewDevice: false
        });
        break;

      case 'security_alert':
        const securityUser: AuthUser = {
          $id: 'test-user-id',
          name: 'Test User',
          email: recipient,
          isAdmin: false,
          totalImagesLimit: 100,
          credits: 200.00,
          avatarUrl: undefined,
          discordId: undefined
        };
        result = await emailService.sendLoginNotification(securityUser, {
          timestamp: new Date().toISOString(),
          ipAddress: '************',
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
          location: 'Unknown Location',
          isNewDevice: true
        });
        break;

      case 'image_limit_warning':
        const limitUser: AuthUser = {
          $id: 'test-user-id',
          name: 'Test User',
          email: recipient,
          isAdmin: false,
          totalImagesLimit: 100,
          credits: 200.00,
          avatarUrl: undefined,
          discordId: undefined
        };
        result = await emailService.sendImageLimitWarning(limitUser, {
          currentCount: 85,
          totalLimit: 100,
          percentage: 85,
          warningType: '80%'
        });
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid email type. Supported types: welcome, order_confirmation, credit_transaction, admin_alert, login_notification, security_alert, image_limit_warning' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: result.success,
      messageId: result.messageId,
      error: result.error,
      type,
      recipient
    });
    
  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/email/test - Get test information
export async function GET() {
  const serviceStatus = emailService.getServiceStatus();
  
  return NextResponse.json({
    message: 'Email testing endpoint',
    serviceStatus,
    usage: {
      'POST /api/email/test': 'Test email sending with { type, recipient }',
      supportedTypes: [
        'welcome - Send welcome email for new users',
        'order_confirmation - Send purchase confirmation email',
        'credit_transaction - Send credit transaction notification',
        'admin_alert - Send admin alert email',
        'login_notification - Send login notification email',
        'security_alert - Send security alert for new device login',
        'image_limit_warning - Send image upload limit warning'
      ]
    },
    example: {
      type: 'welcome',
      recipient: '<EMAIL>'
    }
  });
}
