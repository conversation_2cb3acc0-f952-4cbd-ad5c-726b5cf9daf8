import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/services/emailService';
import { AuthUser, Order } from '@/types';

// POST /api/email/test - Test email functionality
export async function POST(request: NextRequest) {
  try {
    const { type, recipient } = await request.json();
    
    if (!recipient) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    let result;
    
    switch (type) {
      case 'welcome':
        const testUser: AuthUser = {
          $id: 'test-user-id',
          name: 'Test User',
          email: recipient,
          isAdmin: true,
          totalImagesLimit: 100,
          credits: 200.00,
          avatarUrl: undefined,
          discordId: undefined
        };
        result = await emailService.sendWelcomeEmail(testUser);
        break;
        
      case 'order_confirmation':
        const testOrder: Order = {
          $id: 'test-order-id',
          $createdAt: new Date().toISOString(),
          $updatedAt: new Date().toISOString(),
          userId: 'test-user-id',
          orderNumber: 'TEST-' + Date.now(),
          items: [
            {
              planId: 'test-plan-1',
              planName: 'Starter Plan',
              imageStock: 50,
              creditCost: 25,
              quantity: 1
            },
            {
              planId: 'test-plan-2',
              planName: 'Pro Plan',
              imageStock: 200,
              creditCost: 75,
              quantity: 1
            }
          ],
          totalCredits: 100,
          total: 100,
          discountAmount: 0,
          couponCode: undefined,
          status: 'completed',
          purchaseDate: new Date().toISOString(),
          emailSent: false
        };
        result = await emailService.sendPurchaseConfirmation(
          recipient,
          'Test User',
          testOrder
        );
        break;
        
      case 'credit_transaction':
        result = await emailService.sendCreditTransactionNotification(
          recipient,
          'Test User',
          'earned',
          30.00,
          'Daily credits claim',
          230.00
        );
        break;
        
      case 'admin_alert':
        result = await emailService.sendAdminAlert(
          recipient,
          'warning',
          'Test Admin Alert',
          'This is a test admin alert to verify email functionality.',
          {
            testMode: true,
            timestamp: new Date().toISOString(),
            systemStatus: 'operational'
          }
        );
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid email type. Supported types: welcome, order_confirmation, credit_transaction, admin_alert' },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: result.success,
      messageId: result.messageId,
      error: result.error,
      type,
      recipient
    });
    
  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/email/test - Get test information
export async function GET() {
  const serviceStatus = emailService.getServiceStatus();
  
  return NextResponse.json({
    message: 'Email testing endpoint',
    serviceStatus,
    usage: {
      'POST /api/email/test': 'Test email sending with { type, recipient }',
      supportedTypes: [
        'welcome - Send welcome email for new users',
        'order_confirmation - Send purchase confirmation email', 
        'credit_transaction - Send credit transaction notification',
        'admin_alert - Send admin alert email'
      ]
    },
    example: {
      type: 'welcome',
      recipient: '<EMAIL>'
    }
  });
}
