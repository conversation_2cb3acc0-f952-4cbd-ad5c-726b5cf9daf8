import { NextRequest, NextResponse } from 'next/server';
import { emailService, EmailTemplate } from '@/lib/services/emailService';

// POST /api/email/send
export async function POST(request: NextRequest) {
  try {
    const body: EmailTemplate = await request.json();
    
    // Validate required fields
    if (!body.to || !body.subject || !body.html) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, html' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.to)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Send email
    const success = await emailService.sendEmail(body);
    
    if (success) {
      return NextResponse.json(
        { message: 'Email sent successfully' },
        { status: 200 }
      );
    } else {
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/email (for testing)
export async function GET() {
  return NextResponse.json({
    message: 'Email API is running',
    endpoints: {
      'POST /api/email/send': 'Send an email with { to, subject, html }'
    }
  });
}
