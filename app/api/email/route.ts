import { NextRequest, NextResponse } from 'next/server';
import { emailService, EmailTemplate, EmailType } from '@/lib/services/emailService';
import nodemailer from 'nodemailer';

// POST /api/email/send
export async function POST(request: NextRequest) {
  try {
    const body: EmailTemplate & { emailType?: EmailType; timestamp?: string } = await request.json();

    // Validate required fields
    if (!body.to || !body.subject || !body.html) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, html' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.to)) {
      return NextResponse.json(
        { error: 'Invalid email address' },
        { status: 400 }
      );
    }

    // Get SMTP configuration
    const smtpConfig = emailService.getSMTPConfig();

    if (!smtpConfig) {
      // Fallback to service's sendEmail method for simulation
      const result = await emailService.sendEmail(body, body.emailType || 'admin_alert');

      if (result.success) {
        return NextResponse.json(
          {
            message: 'Email sent successfully (simulated)',
            messageId: result.messageId
          },
          { status: 200 }
        );
      } else {
        return NextResponse.json(
          { error: result.error || 'Failed to send email' },
          { status: 500 }
        );
      }
    }

    // Create nodemailer transporter with SMTP config
    const transporter = nodemailer.createTransport({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: smtpConfig.auth.user,
        pass: smtpConfig.auth.pass,
      },
      tls: {
        rejectUnauthorized: false // Allow self-signed certificates in development
      }
    });

    // Verify SMTP connection
    try {
      await transporter.verify();
    } catch (verifyError) {
      console.error('SMTP verification failed:', verifyError);
      return NextResponse.json(
        { error: 'SMTP configuration error' },
        { status: 500 }
      );
    }

    // Send email via SMTP
    const mailOptions = {
      from: body.from || `${smtpConfig.from.name} <${smtpConfig.from.email}>`,
      to: body.to,
      subject: body.subject,
      html: body.html,
      replyTo: body.replyTo
    };

    const info = await transporter.sendMail(mailOptions);

    return NextResponse.json(
      {
        message: 'Email sent successfully',
        messageId: info.messageId,
        accepted: info.accepted,
        rejected: info.rejected
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email API error:', error);

    // Provide more specific error messages
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      if (error.message.includes('EAUTH')) {
        errorMessage = 'SMTP authentication failed';
      } else if (error.message.includes('ECONNECTION')) {
        errorMessage = 'SMTP connection failed';
      } else if (error.message.includes('ETIMEDOUT')) {
        errorMessage = 'SMTP connection timeout';
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET /api/email (for testing and status)
export async function GET() {
  const serviceStatus = emailService.getServiceStatus();

  return NextResponse.json({
    message: 'Email API is running',
    status: serviceStatus,
    endpoints: {
      'POST /api/email/send': 'Send an email with { to, subject, html, emailType?, from?, replyTo? }',
      'GET /api/email': 'Get email service status and configuration'
    },
    supportedEmailTypes: [
      'order_confirmation',
      'welcome',
      'credit_transaction',
      'admin_alert',
      'password_reset',
      'account_verification'
    ]
  });
}
