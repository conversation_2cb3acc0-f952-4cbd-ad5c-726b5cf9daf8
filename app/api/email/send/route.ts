import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import { loggingService } from '@/lib/services/loggingService';

interface EmailRequest {
  to: string;
  subject: string;
  html: string;
  from?: string;
  replyTo?: string;
  emailType?: string;
  timestamp?: string;
}

// POST /api/email/send - Send email via SMTP
export async function POST(request: NextRequest) {
  try {
    const emailData: EmailRequest = await request.json();
    
    // Validate required fields
    if (!emailData.to || !emailData.subject || !emailData.html) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, html' },
        { status: 400 }
      );
    }

    // Get SMTP configuration from environment variables
    const smtpConfig = {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    };

    const fromName = process.env.SMTP_FROM_NAME || 'AveImgCloud';
    const fromEmail = process.env.SMTP_FROM_EMAIL || '<EMAIL>';

    // Check if SMTP is configured
    if (!smtpConfig.host || !smtpConfig.auth.user || !smtpConfig.auth.pass) {
      // Log the attempt
      await loggingService.logSystemEvent({
        type: 'info',
        action: 'email_service',
        details: 'Email sending attempted but SMTP not configured - using test mode',
        metadata: {
          emailType: emailData.emailType,
          recipient: emailData.to,
          timestamp: new Date().toISOString()
        },
        severity: 'low'
      });

      // Return simulated success for development
      const messageId = `sim_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      return NextResponse.json({
        success: true,
        messageId,
        testMode: true,
        message: 'Email simulated (SMTP not configured)'
      });
    }

    // Create transporter
    const transporter = nodemailer.createTransport(smtpConfig);

    // Verify connection
    try {
      await transporter.verify();
    } catch (verifyError) {
      console.error('SMTP verification failed:', verifyError);
      
      await loggingService.logSystemEvent({
        type: 'error',
        action: 'email_service',
        details: 'SMTP verification failed',
        metadata: {
          error: verifyError instanceof Error ? verifyError.message : 'Unknown error',
          emailType: emailData.emailType,
          recipient: emailData.to
        },
        severity: 'high'
      });

      return NextResponse.json(
        { error: 'SMTP configuration error' },
        { status: 500 }
      );
    }

    // Prepare email options
    const mailOptions = {
      from: emailData.from || `${fromName} <${fromEmail}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      replyTo: emailData.replyTo,
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);

    // Log success
    await loggingService.logSystemEvent({
      type: 'info',
      action: 'email_service',
      details: 'Email sent successfully via SMTP',
      metadata: {
        emailType: emailData.emailType,
        recipient: emailData.to,
        messageId: info.messageId,
        timestamp: new Date().toISOString()
      },
      severity: 'low'
    });

    console.log('✅ Email sent successfully:', info.messageId);

    return NextResponse.json({
      success: true,
      messageId: info.messageId,
      testMode: false
    });

  } catch (error) {
    console.error('Email sending error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    // Log error
    try {
      await loggingService.logSystemEvent({
        type: 'error',
        action: 'email_service',
        details: `Email sending failed: ${errorMessage}`,
        metadata: {
          error: errorMessage,
          timestamp: new Date().toISOString()
        },
        severity: 'high'
      });
    } catch (logError) {
      console.error('Failed to log email error:', logError);
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET /api/email/send - Get endpoint information
export async function GET() {
  return NextResponse.json({
    message: 'Email sending endpoint',
    usage: 'POST with { to, subject, html, from?, replyTo?, emailType? }',
    smtpConfigured: !!(process.env.SMTP_HOST && process.env.SMTP_USER && process.env.SMTP_PASS)
  });
}
