import { NextRequest, NextResponse } from 'next/server';
import { Client, Users } from 'node-appwrite';

// Server-side Appwrite client for admin operations
const client = new Client();
client
  .setEndpoint(process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT || 'https://api.avehubs.com/v1')
  .setProject(process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '')
  .setKey(process.env.APPWRITE_API_KEY || ''); // Server-side API key

const users = new Users(client);

export async function POST(request: NextRequest) {
  try {
    const { userIds } = await request.json();
    
    if (!Array.isArray(userIds)) {
      return NextResponse.json({
        success: false,
        error: 'userIds must be an array'
      }, { status: 400 });
    }

    // Fetch user names from Appwrite Auth
    const userNames: Record<string, { name: string; email: string }> = {};
    
    for (const userId of userIds) {
      try {
        const user = await users.get(userId);
        userNames[userId] = {
          name: user.name || 'Unknown User',
          email: user.email || ''
        };
      } catch (error) {
        console.warn(`Could not fetch user ${userId}:`, error);
        userNames[userId] = {
          name: 'Unknown User',
          email: ''
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: userNames
    });
  } catch (error) {
    console.error('Error fetching user names:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to fetch user names'
    }, { status: 500 });
  }
}
