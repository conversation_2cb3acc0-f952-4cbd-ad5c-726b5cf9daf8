import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/lib/appwrite/auth';

// POST /api/auth/track-login - Track login event and send notifications
export async function POST(request: NextRequest) {
  try {
    // Get current user
    const user = await authService.getCurrentUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Track the login event with request context
    await authService.trackLoginEvent(user, request);

    return NextResponse.json({
      success: true,
      message: 'Login event tracked successfully'
    });

  } catch (error) {
    console.error('Login tracking error:', error);
    return NextResponse.json(
      { error: 'Failed to track login event' },
      { status: 500 }
    );
  }
}

// GET /api/auth/track-login - Get endpoint information
export async function GET() {
  return NextResponse.json({
    message: 'Login tracking endpoint',
    usage: 'POST to track login events with IP and user agent detection'
  });
}
