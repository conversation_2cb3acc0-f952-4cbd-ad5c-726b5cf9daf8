'use client';

import React, { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserStats } from '@/hooks/useUserStats';
import { useShoppingCart } from '@/hooks/useShoppingCart';
import { formatCurrency } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { ToastContainer, useToast } from '@/components/ui/Toast';

export default function CheckoutPage() {
  const { user } = useAuth();
  const { stats, deductCredits, addCredits } = useUserStats();
  const { 
    cartItems,
    cartSummary,
    getCheckoutData,
    processCheckout,
    applyCoupon,
    removeCoupon,
    appliedCoupon,
    couponError,
    hasDiscount,
    getDiscountText,
    isEmpty,
    isLoading: cartLoading
  } = useShoppingCart();
  const toast = useToast();

  const [couponCode, setCouponCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderComplete, setOrderComplete] = useState(false);
  const [orderDetails, setOrderDetails] = useState<any>(null);

  // Handle coupon application
  const handleApplyCoupon = async (e: React.FormEvent) => {
    e.preventDefault();
    if (couponCode.trim()) {
      await applyCoupon(couponCode.trim());
    }
  };

  // Handle order processing
  const handleProcessOrder = async () => {
    if (!user || !stats) return;

    const checkoutData = getCheckoutData();

    try {
      setIsProcessing(true);

      // Check if user has enough credits for the discounted total
      if (stats.credits < checkoutData.total) {
        throw new Error(`Insufficient credits. You need ${checkoutData.total - stats.credits} more credits.`);
      }

      // Deduct credits first
      await deductCredits(checkoutData.total, 'Store purchase');

      // Process checkout (pass user's original credits, but validation is already done above)
      const order = await processCheckout(stats.credits);

      // Add image stock to user's limit
      if (checkoutData.totalImageStock > 0) {
        await addCredits(0, `Added ${checkoutData.totalImageStock} image uploads from store purchase`);
        // Note: This should actually update image limit, not credits
        // You might need to add a separate method for updating image limits
      }

      setOrderDetails(order);
      setOrderComplete(true);
    } catch (error) {
      console.error('Checkout failed:', error);
      // Error is handled by the hooks
    } finally {
      setIsProcessing(false);
    }
  };

  // Redirect if cart is empty
  if (isEmpty && !orderComplete) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0L4 5m0 0H2m5 8h10m0 0a2 2 0 100 4 2 2 0 000-4zm-10 0a2 2 0 100 4 2 2 0 000-4z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Your cart is empty
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Add some plans to your cart before checking out.
          </p>
          <Button variant="primary" onClick={() => window.location.href = '/store'}>
            Browse Store
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // Order complete view
  if (orderComplete && orderDetails) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto py-12">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
            {/* Success Icon */}
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>

            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              🎉 Order Successful!
            </h1>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Your purchase has been completed successfully. Your account has been upgraded with additional image uploads.
            </p>

            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 mb-6">
              <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">Order Number</div>
              <div className="text-lg font-mono font-semibold text-gray-900 dark:text-white">
                {orderDetails.orderNumber}
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="font-semibold text-gray-900 dark:text-white">
                  {formatCurrency(orderDetails.totalCredits)}
                </div>
                <div className="text-gray-500 dark:text-gray-400">Credits Spent</div>
              </div>
              <div className="text-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="font-semibold text-gray-900 dark:text-white">
                  +{orderDetails.totalImageStock || 0}
                </div>
                <div className="text-gray-500 dark:text-gray-400">Image Uploads</div>
              </div>
            </div>

            <div className="space-y-3">
              <Button 
                variant="primary" 
                onClick={() => window.location.href = '/dashboard'}
                className="w-full"
              >
                Return to Dashboard
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/store'}
                className="w-full"
              >
                Continue Shopping
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const checkoutData = getCheckoutData();

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Checkout
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Review your order and complete your purchase
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Order Summary */}
          <div className="lg:col-span-2 space-y-6">
            {/* Cart Items */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Order Items
              </h3>
              
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <div key={item.$id} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {item.plan?.name || 'Unknown Plan'}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        +{item.plan?.imageStock || 0} image uploads
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-gray-900 dark:text-white">
                        {formatCurrency(item.plan?.creditCost || 0)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">credits</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Coupon Code */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Coupon Code
              </h3>

              {appliedCoupon ? (
                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <div>
                      <div className="font-medium text-green-800 dark:text-green-200">
                        Coupon Applied: {appliedCoupon?.code || 'N/A'}
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400">
                        {getDiscountText()}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={removeCoupon}
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <form onSubmit={handleApplyCoupon} className="space-y-4">
                  <Input
                    type="text"
                    placeholder="Enter coupon code"
                    value={couponCode}
                    onChange={setCouponCode}
                    error={couponError || undefined}
                  />
                  <Button
                    type="submit"
                    variant="outline"
                    disabled={!couponCode.trim() || cartLoading}
                    loading={cartLoading}
                  >
                    Apply Coupon
                  </Button>
                </form>
              )}
            </div>
          </div>

          {/* Payment Summary */}
          <div className="space-y-6">
            {/* Account Balance */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Account Balance
              </h3>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-600 dark:text-cyan-400 mb-2">
                  {formatCurrency(stats?.credits || 0)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Available Credits</div>
              </div>
            </div>

            {/* Order Total */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Order Summary
              </h3>
              
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Subtotal</span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {formatCurrency(checkoutData.subtotal)}
                  </span>
                </div>

                {hasDiscount && (
                  <div className="flex justify-between text-sm">
                    <span className="text-green-600 dark:text-green-400">Discount</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      -{formatCurrency(checkoutData.discount)}
                    </span>
                  </div>
                )}

                <hr className="border-gray-200 dark:border-gray-700" />

                <div className="flex justify-between">
                  <span className="text-lg font-semibold text-gray-900 dark:text-white">Total</span>
                  <span className="text-lg font-bold text-cyan-600 dark:text-cyan-400">
                    {formatCurrency(checkoutData.total)}
                  </span>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
                  You'll receive +{checkoutData.totalImageStock} image uploads
                </div>
              </div>
            </div>

            {/* Payment Button */}
            <div className="space-y-4">
              {stats && stats.credits < checkoutData.total ? (
                <div className="space-y-4">
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                    <div className="text-center">
                      <div className="text-yellow-600 dark:text-yellow-400 text-sm font-medium mb-2">
                        ⚠️ Insufficient Credits
                      </div>
                      <div className="text-yellow-600 dark:text-yellow-400 text-xs mb-3">
                        You need {formatCurrency(checkoutData.total - (stats?.credits || 0))} more credits to complete this purchase
                      </div>
                      {!appliedCoupon ? (
                        <div className="bg-cyan-50 dark:bg-cyan-900/20 border border-cyan-200 dark:border-cyan-800 rounded-lg p-3 mb-3">
                          <div className="text-cyan-600 dark:text-cyan-400 text-sm font-medium mb-1">
                            💡 Have a coupon code?
                          </div>
                          <div className="text-cyan-600 dark:text-cyan-400 text-xs">
                            Apply a coupon code above to reduce the total and complete your purchase
                          </div>
                        </div>
                      ) : (
                        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-3">
                          <div className="text-green-600 dark:text-green-400 text-xs">
                            ✅ Coupon applied! {stats.credits >= checkoutData.total ? 'You can now complete your purchase.' : 'You still need more credits or a better coupon.'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    className="w-full"
                    disabled
                  >
                    Insufficient Credits
                  </Button>
                </div>
              ) : (
                <Button
                  variant="primary"
                  className="w-full"
                  onClick={handleProcessOrder}
                  disabled={isProcessing || cartLoading}
                  loading={isProcessing}
                >
                  {isProcessing ? 'Processing...' : `Complete Purchase (${formatCurrency(checkoutData.total)})`}
                </Button>
              )}

              <Button
                variant="outline"
                className="w-full"
                onClick={() => window.location.href = '/store'}
                disabled={isProcessing}
              >
                Back to Store
              </Button>
            </div>

            {/* Security Notice */}
            <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <svg className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                </svg>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  <div className="font-medium mb-1">Secure Transaction</div>
                  <div>
                    Your purchase is secured and processed using your existing credit balance. 
                    No external payment processing required.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
    </DashboardLayout>
  );
}
