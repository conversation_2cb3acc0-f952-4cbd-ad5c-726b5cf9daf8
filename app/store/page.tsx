'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserStats } from '@/hooks/useUserStats';
import { useShoppingCart } from '@/hooks/useShoppingCart';
import { storeService } from '@/lib/services/storeService';
import { StorePlan } from '@/types';
import { formatCurrency } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import Button from '@/components/ui/Button';
import { requestManager } from '@/lib/services/requestManager';

export default function StorePage() {
  const { user } = useAuth();
  const { stats } = useUserStats();
  const { 
    addToCart, 
    removeFromCart, 
    isInCart, 
    cartSummary, 
    error: cartError 
  } = useShoppingCart();
  
  const [plans, setPlans] = useState<StorePlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Fetch active plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Use request manager for cached active plans
        const activePlans = await requestManager.fetchActiveStorePlans();
        setPlans(activePlans);
      } catch (err) {
        console.error('Failed to fetch plans:', err);
        setError(err instanceof Error ? err.message : 'Failed to load store plans');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // Handle add to cart
  const handleAddToCart = async (planId: string) => {
    try {
      setActionLoading(planId);
      await addToCart(planId);
    } catch (err) {
      console.error('Failed to add to cart:', err);
      // Error is handled by the cart hook
    } finally {
      setActionLoading(null);
    }
  };

  // Handle remove from cart
  const handleRemoveFromCart = async (planId: string) => {
    try {
      setActionLoading(planId);
      const cartItem = cartSummary.items.find(item => item.planId === planId);
      if (cartItem) {
        await removeFromCart(cartItem.$id);
      }
    } catch (err) {
      console.error('Failed to remove from cart:', err);
      // Error is handled by the cart hook
    } finally {
      setActionLoading(null);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading store plans...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Failed to Load Store
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            💎 Premium Plans
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Upgrade your image hosting experience with additional uploads and enhanced features.
            Purchase plans using your credit balance.
          </p>
        </div>

        {/* Current Status */}
        {user && stats && (
          <div className="bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-xl p-6 border border-cyan-200/50 dark:border-cyan-700/50">
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div className="space-y-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Your Account Status
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Current balance and usage information
                </p>
              </div>
              
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                    {formatCurrency(stats.credits)}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">Credits</div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {stats.currentImages}/{stats.totalImagesLimit}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">Images</div>
                </div>

                {cartSummary.totalItems > 0 && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {cartSummary.totalItems}
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">In Cart</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Error Messages */}
        {cartError && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-600 dark:text-red-400 text-sm">{cartError}</p>
          </div>
        )}

        {/* Plans Grid */}
        {plans.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Plans Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Store plans will appear here when they become available.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <div
                key={plan.$id}
                className={`
                  relative bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-200 hover:shadow-xl hover:border-cyan-300 dark:hover:border-cyan-600
                  ${plan.popularTag ? 'ring-2 ring-cyan-500 ring-opacity-50' : ''}
                `}
              >
                {/* Popular Badge */}
                {plan.popularTag && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-gradient-to-r from-cyan-500 to-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full">
                      POPULAR
                    </div>
                  </div>
                )}

                {/* Plan Header */}
                <div className="text-center mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {plan.name}
                  </h3>
                  <div className="text-3xl font-bold text-cyan-600 dark:text-cyan-400 mb-1">
                    {formatCurrency(plan.creditCost)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    credits
                  </div>
                </div>

                {/* Plan Features */}
                <div className="space-y-4 mb-6">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span>
                      <strong className="text-gray-900 dark:text-white">+{plan.imageStock}</strong> additional image uploads
                    </span>
                  </div>

                  {plan.features && plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                      <svg className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>{feature}</span>
                    </div>
                  ))}

                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {plan.description}
                    </p>
                  </div>
                </div>

                {/* Action Button */}
                <div className="space-y-3">
                  {isInCart(plan.$id) ? (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => handleRemoveFromCart(plan.$id)}
                      disabled={actionLoading === plan.$id}
                      loading={actionLoading === plan.$id}
                    >
                      {actionLoading === plan.$id ? 'Removing...' : 'Remove from Cart'}
                    </Button>
                  ) : (
                    <Button
                      variant="primary"
                      className="w-full"
                      onClick={() => handleAddToCart(plan.$id)}
                      disabled={actionLoading === plan.$id || (stats ? stats.credits < plan.creditCost : false)}
                      loading={actionLoading === plan.$id}
                    >
                      {actionLoading === plan.$id ? 'Adding...' : 'Add to Cart'}
                    </Button>
                  )}

                  {stats && stats.credits < plan.creditCost && !isInCart(plan.$id) && (
                    <p className="text-xs text-red-600 dark:text-red-400 text-center">
                      Insufficient credits (need {formatCurrency(plan.creditCost - stats.credits)} more)
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Cart Summary */}
        {cartSummary.totalItems > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Cart Summary
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {cartSummary.totalItems} item{cartSummary.totalItems !== 1 ? 's' : ''}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="text-2xl font-bold text-cyan-600 dark:text-cyan-400">
                  {formatCurrency(cartSummary.totalCredits)}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Total Cost</div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  +{cartSummary.totalImageStock}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">Image Uploads</div>
              </div>

              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {stats ? stats.totalImagesLimit + cartSummary.totalImageStock : cartSummary.totalImageStock}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">New Limit</div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                variant="primary"
                onClick={() => window.location.href = '/store/checkout'}
                className="px-8"
              >
                Proceed to Checkout
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}
