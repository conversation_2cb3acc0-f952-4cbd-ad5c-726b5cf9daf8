'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { loggingService, LogFilters } from '@/lib/services/loggingService';
import { AdminLog } from '@/types';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProtectedAdminRoute from '@/components/auth/ProtectedAdminRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

export default function AdminLogsPage() {
  return (
    <ProtectedAdminRoute>
      <AdminLogsContent />
    </ProtectedAdminRoute>
  );
}

function AdminLogsContent() {
  useAuth(); // Ensure user is authenticated
  const [logs, setLogs] = useState<AdminLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<LogFilters>({
    action: '',
    adminUserId: '',
    targetType: '',
    success: 'all',
    dateFrom: '',
    dateTo: '',
    searchTerm: ''
  });
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [logStats, setLogStats] = useState({
    totalLogs: 0,
    successfulActions: 0,
    failedActions: 0,
    errorCount: 0,
    warningCount: 0
  });
  const [autoRefresh, setAutoRefresh] = useState(false);

  // Fetch logs and stats
  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Use enhanced logging service with filters
        const logsData = await loggingService.getAdminLogs(50, (page - 1) * 50, filters);

        if (page === 1) {
          setLogs(logsData);
          // Fetch stats only on first page load
          const stats = await loggingService.getLogStats(filters.dateFrom, filters.dateTo);
          setLogStats(stats);
        } else {
          setLogs(prev => [...prev, ...logsData]);
        }

        setHasMore(logsData.length === 50);
      } catch (err) {
        console.error('Failed to fetch logs:', err);
        setError(err instanceof Error ? err.message : 'Failed to load logs');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLogs();
  }, [page, filters]);

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(async () => {
      try {
        const latestLogs = await loggingService.getAdminLogs(50, 0, filters);
        setLogs(latestLogs);
        const stats = await loggingService.getLogStats(filters.dateFrom, filters.dateTo);
        setLogStats(stats);
      } catch (err) {
        console.error('Auto-refresh failed:', err);
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, filters]);

  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPage(1);
  };

  const clearFilters = () => {
    setFilters({
      action: '',
      adminUserId: '',
      targetType: '',
      success: 'all',
      dateFrom: '',
      dateTo: '',
      searchTerm: ''
    });
    setPage(1);
  };

  const handleSearch = async () => {
    if (!filters.searchTerm?.trim()) return;

    try {
      setIsLoading(true);
      const searchResults = await loggingService.searchLogs(filters.searchTerm, 50, 0);
      setLogs(searchResults);
      setPage(1);
      setHasMore(searchResults.length === 50);
    } catch (err) {
      console.error('Search failed:', err);
      setError('Search failed');
    } finally {
      setIsLoading(false);
    }
  };

  const loadMore = () => {
    setPage(prev => prev + 1);
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      // User management
      case 'create_user':
      case 'update_user':
      case 'update_user_status':
        return '👤';

      // Store operations
      case 'create_store_plan':
      case 'update_store_plan':
      case 'delete_store_plan':
        return '🏪';

      // Coupon management
      case 'create_coupon':
      case 'update_coupon':
      case 'delete_coupon':
        return '🎫';

      // Credit transactions
      case 'add_credits':
      case 'refund_order':
      case 'credit_purchase':
      case 'credit_daily_earn':
      case 'credit_admin_credit':
      case 'credit_manual_adjustment':
        return '💰';

      // Security actions
      case 'suspend_user':
      case 'ban_user':
        return '🔒';

      // Authentication events
      case 'login':
      case 'logout':
      case 'oauth_start':
      case 'oauth_success':
      case 'oauth_failure':
        return '🔐';

      // Image operations
      case 'image_upload':
      case 'image_delete':
      case 'image_view':
      case 'image_share':
        return '🖼️';

      // System events
      case 'system_error':
        return '❌';
      case 'system_warning':
        return '⚠️';

      default:
        return '📝';
    }
  };

  const getActionColor = (success: boolean) => {
    return success 
      ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200'
      : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
  };

  if (isLoading && page === 1) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading admin logs...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                📋 Admin Activity Logs
              </h1>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
                Monitor and track all administrative actions and system events
              </p>
            </div>
            <div className="flex items-center space-x-4 mt-4 sm:mt-0">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="auto-refresh"
                  checked={autoRefresh}
                  onChange={(e) => setAutoRefresh(e.target.checked)}
                  className="rounded border-gray-300 text-cyan-600 focus:ring-cyan-500"
                />
                <label htmlFor="auto-refresh" className="text-sm text-gray-600 dark:text-gray-400">
                  Auto-refresh (30s)
                </label>
              </div>
              {autoRefresh && (
                <div className="flex items-center space-x-1 text-cyan-600">
                  <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse"></div>
                  <span className="text-xs">Live</span>
                </div>
              )}
            </div>
          </div>

          {/* Statistics Cards */}
          <div className="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{logStats.totalLogs}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Logs</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-green-600">{logStats.successfulActions}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Successful</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-red-600">{logStats.failedActions}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Failed</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-orange-600">{logStats.errorCount}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Errors</div>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
              <div className="text-2xl font-bold text-yellow-600">{logStats.warningCount}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Warnings</div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-6">
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 sm:mb-0">
                Filter Logs
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="w-full sm:w-auto"
              >
                Clear Filters
              </Button>
            </div>
            
            {/* Search Bar */}
            <div className="mb-4">
              <div className="flex flex-col sm:flex-row gap-2">
                <div className="flex-1">
                  <Input
                    label="Search Logs"
                    type="text"
                    placeholder="Search in log details..."
                    value={filters.searchTerm || ''}
                    onChange={(value) => handleFilterChange('searchTerm', value)}
                  />
                </div>
                <div className="flex space-x-2 sm:mt-6">
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSearch}
                    disabled={!filters.searchTerm?.trim()}
                    className="whitespace-nowrap"
                  >
                    🔍 Search
                  </Button>
                </div>
              </div>
            </div>

            {/* Filter Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              <Input
                label="Action"
                type="text"
                placeholder="e.g., create_user"
                value={filters.action || ''}
                onChange={(value) => handleFilterChange('action', value)}
              />

              <Input
                label="Admin User ID"
                type="text"
                placeholder="Filter by admin"
                value={filters.adminUserId || ''}
                onChange={(value) => handleFilterChange('adminUserId', value)}
              />

              <Input
                label="Target Type"
                type="text"
                placeholder="e.g., user, plan"
                value={filters.targetType || ''}
                onChange={(value) => handleFilterChange('targetType', value)}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={filters.success === true ? 'true' : filters.success === false ? 'false' : 'all'}
                  onChange={(e) => {
                    const value = e.target.value;
                    handleFilterChange('success', value === 'all' ? 'all' : value === 'true');
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                >
                  <option value="all">All</option>
                  <option value="true">Success</option>
                  <option value="false">Failed</option>
                </select>
              </div>

              <Input
                label="Date From"
                type="date"
                value={filters.dateFrom || ''}
                onChange={(value) => handleFilterChange('dateFrom', value)}
              />

              <Input
                label="Date To"
                type="date"
                value={filters.dateTo || ''}
                onChange={(value) => handleFilterChange('dateTo', value)}
              />
            </div>
          </div>
        </div>

        {/* Logs List */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Activity Logs ({logs.length})
            </h3>
          </div>

          {error && (
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
              </div>
            </div>
          )}

          {logs.length === 0 ? (
            <div className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Logs Found
              </h4>
              <p className="text-gray-600 dark:text-gray-400">
                No admin activity logs match your current filters.
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {logs.map((log) => (
                <div key={log.$id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-3 sm:space-y-0">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-lg ${getActionColor(log.success)}`}>
                          {getActionIcon(log.action)}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {log.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getActionColor(log.success)}`}>
                            {log.success ? 'Success' : 'Failed'}
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {log.details}
                        </p>
                        
                        <div className="flex flex-wrap items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                          <span>Admin: {log.adminUserId}</span>
                          {log.targetType && <span>Target: {log.targetType}</span>}
                          {log.targetId && <span>ID: {log.targetId}</span>}
                          {log.ipAddress && <span>IP: {log.ipAddress}</span>}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex-shrink-0 text-right">
                      <p className="text-sm text-gray-900 dark:text-white">
                        {new Date(log.$createdAt).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(log.$createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Load More */}
          {hasMore && logs.length > 0 && (
            <div className="p-6 border-t border-gray-200 dark:border-gray-700 text-center">
              <Button
                variant="outline"
                onClick={loadMore}
                loading={isLoading}
                className="w-full sm:w-auto"
              >
                Load More Logs
              </Button>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
