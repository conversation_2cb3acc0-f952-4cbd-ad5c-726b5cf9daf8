'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { adminService } from '@/lib/services/adminService';
import { AdminStats } from '@/types';
import { formatCurrency, formatNumber } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProtectedAdminRoute from '@/components/auth/ProtectedAdminRoute';
import Button from '@/components/ui/Button';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { requestManager } from '@/lib/services/requestManager';
import { useAdminStatsRefresh } from '@/hooks/useAutoRefresh';
import RefreshStatus from '@/components/ui/RefreshStatus';

export default function AdminDashboardPage() {
  return (
    <ProtectedAdminRoute>
      <AdminDashboardContent />
    </ProtectedAdminRoute>
  );
}

function AdminDashboardContent() {
  const { user } = useAuth();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);
  const toast = useToast();

  // Auto-refresh hook for admin stats
  const {
    data: refreshedStats,
    isRefreshing,
    lastRefresh,
    error: refreshError,
    refresh: manualRefresh,
    refreshCount
  } = useAdminStatsRefresh({
    enabled: autoRefreshEnabled,
    interval: 60000, // 1 minute
    onError: (error) => {
      console.error('Admin stats refresh failed:', error);
      toast.showError('Refresh failed', error.message);
    },
    onRefresh: () => {
      console.log('Admin stats refreshed successfully');
    }
  });

  // Fetch admin stats (initial load)
  useEffect(() => {
    const fetchStats = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Use request manager for cached admin stats
        const adminStats = await requestManager.fetchAdminStats();
        setStats(adminStats);
      } catch (err) {
        console.error('Failed to fetch admin stats:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load admin statistics';
        setError(errorMessage);
        toast.showError('Error loading dashboard', errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  // Use refreshed stats if available, otherwise use initial stats
  const currentStats = refreshedStats || stats;

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading admin dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto text-center py-12">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
              Failed to Load Dashboard
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
            >
              Try Again
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                🔧 Admin Dashboard
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                System overview and management tools
              </p>
            </div>

            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/admin/logs'}
              >
                View Logs
              </Button>
              <Button
                variant="primary"
                onClick={() => window.location.href = '/admin/users'}
              >
                Manage Users
              </Button>
            </div>
          </div>

          {/* Refresh Status */}
          <RefreshStatus
            isRefreshing={isRefreshing}
            lastRefresh={lastRefresh}
            error={refreshError}
            refreshCount={refreshCount}
            onManualRefresh={manualRefresh}
            onToggleAutoRefresh={setAutoRefreshEnabled}
            autoRefreshEnabled={autoRefreshEnabled}
            showControls={true}
            showStats={true}
            className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          />
        </div>

        {/* Stats Grid */}
        {currentStats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Users */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                  +{formatNumber(currentStats.newUsersThisMonth)} this month
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {formatNumber(currentStats.totalUsers)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Users</div>
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                {formatNumber(currentStats.activeUsers)} active (30 days)
              </div>
            </div>

            {/* Total Revenue */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                  +{formatCurrency(currentStats.revenueThisMonth)} this month
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {formatCurrency(currentStats.totalRevenue)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Revenue</div>
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Credits collected from purchases
              </div>
            </div>

            {/* Total Orders */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                  +{formatNumber(currentStats.ordersThisMonth)} this month
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {formatNumber(currentStats.totalOrders)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Orders</div>
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Completed store purchases
              </div>
            </div>

            {/* Total Images */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-cyan-100 dark:bg-cyan-900/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-cyan-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                {formatNumber(currentStats.totalImages)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Images</div>
              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Uploaded by all users
              </div>
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* User Management */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                User Management
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
              Manage user accounts, credits, and image limits. View user activity and make adjustments as needed.
            </p>
            <Button
              variant="primary"
              className="w-full"
              onClick={() => window.location.href = '/admin/users'}
            >
              Manage Users
            </Button>
          </div>

          {/* Store Management */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Store Management
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
              Create and manage subscription plans, set pricing, and monitor store performance.
            </p>
            <Button
              variant="primary"
              className="w-full"
              onClick={() => window.location.href = '/admin/store'}
            >
              Manage Store
            </Button>
          </div>

          {/* Orders & Analytics */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Orders & Analytics
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
              View order history, transaction logs, and system analytics to monitor platform performance.
            </p>
            <Button
              variant="primary"
              className="w-full"
              onClick={() => window.location.href = '/admin/orders'}
            >
              View Orders
            </Button>
          </div>
        </div>

        {/* Recent Activity Summary */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            This Month's Activity
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                {stats ? formatNumber(stats.newUsersThisMonth) : '0'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">New Users</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                {stats ? formatCurrency(stats.revenueThisMonth) : formatCurrency(0)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Revenue</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                {stats ? formatNumber(stats.ordersThisMonth) : '0'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Orders</div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200/50 dark:border-green-700/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-green-800 dark:text-green-200">
                  System Status: Operational
                </h4>
                <p className="text-sm text-green-600 dark:text-green-400">
                  All systems are running normally
                </p>
              </div>
            </div>
            
            <div className="text-sm text-green-600 dark:text-green-400">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>
        </div>

        <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
      </div>
    </DashboardLayout>
  );
}
