'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { storeService } from '@/lib/services/storeService';
import { adminService } from '@/lib/services/adminService';
import { StorePlan } from '@/types';
import { formatCurrency } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProtectedAdminRoute from '@/components/auth/ProtectedAdminRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { requestManager } from '@/lib/services/requestManager';

interface PlanFormData {
  name: string;
  description: string;
  imageStock: number;
  creditCost: number;
  isActive: boolean;
  features: string[];
  popularTag: boolean;
}

export default function AdminStorePage() {
  return (
    <ProtectedAdminRoute>
      <AdminStoreContent />
    </ProtectedAdminRoute>
  );
}

function AdminStoreContent() {
  const { user } = useAuth();
  const [plans, setPlans] = useState<StorePlan[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingPlan, setEditingPlan] = useState<StorePlan | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<PlanFormData>({
    name: '',
    description: '',
    imageStock: 50,
    creditCost: 100,
    isActive: true,
    features: [],
    popularTag: false
  });
  const [featureInput, setFeatureInput] = useState('');

  // Toast
  const toast = useToast();

  // Fetch plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const allPlans = await requestManager.fetchAdminStorePlans();
        setPlans(allPlans);
      } catch (err) {
        console.error('Failed to fetch plans:', err);
        setError(err instanceof Error ? err.message : 'Failed to load store plans');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, []);

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      imageStock: 50,
      creditCost: 100,
      isActive: true,
      features: [],
      popularTag: false
    });
    setFeatureInput('');
    setEditingPlan(null);
    setShowCreateForm(false);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      setActionLoading(editingPlan ? editingPlan.$id : 'create');
      
      const planData = {
        ...formData,
        createdBy: user.$id
      };

      if (editingPlan) {
        // Update existing plan
        const updatedPlan = await storeService.updatePlan(editingPlan.$id, planData);
        setPlans(plans.map(p => p.$id === editingPlan.$id ? updatedPlan : p));
        
        // Invalidate store plan caches
        requestManager.invalidatePattern('storePlans');
        
        // Log admin action
        await adminService.logAdminAction({
          adminUserId: user.$id,
          action: 'update_store_plan',
          targetType: 'plan',
          targetId: editingPlan.$id,
          details: `Updated plan: ${planData.name}`,
          success: true
        });

        toast.showSuccess('Plan updated successfully');
      } else {
        // Create new plan
        const newPlan = await storeService.createPlan(planData);
        setPlans([newPlan, ...plans]);
        
        // Invalidate store plan caches
        requestManager.invalidatePattern('storePlans');
        
        // Log admin action
        await adminService.logAdminAction({
          adminUserId: user.$id,
          action: 'create_store_plan',
          targetType: 'plan',
          targetId: newPlan.$id,
          details: `Created new plan: ${planData.name}`,
          success: true
        });

        toast.showSuccess('Plan created successfully');
      }

      resetForm();
    } catch (err) {
      console.error('Failed to save plan:', err);
      setError(err instanceof Error ? err.message : 'Failed to save plan');
      
      // Log failed admin action
      await adminService.logAdminAction({
        adminUserId: user.$id,
        action: editingPlan ? 'update_store_plan' : 'create_store_plan',
        targetType: 'plan',
        targetId: editingPlan?.$id,
        details: `Failed to ${editingPlan ? 'update' : 'create'} plan: ${err instanceof Error ? err.message : 'Unknown error'}`,
        success: false
      });

      toast.showError('Failed to save plan', err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setActionLoading(null);
    }
  };

  // Handle plan deletion
  const handleDeletePlan = async (plan: StorePlan) => {
    if (!user) return;
    
    if (!confirm(`Are you sure you want to delete the plan "${plan.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setActionLoading(plan.$id);
      await storeService.deletePlan(plan.$id);
      setPlans(plans.filter(p => p.$id !== plan.$id));
      
      // Invalidate store plan caches
      requestManager.invalidatePattern('storePlans');
      
      // Log admin action
      await adminService.logAdminAction({
        adminUserId: user.$id,
        action: 'delete_store_plan',
        targetType: 'plan',
        targetId: plan.$id,
        details: `Deleted plan: ${plan.name}`,
        success: true
      });

      toast.showSuccess('Plan deleted successfully');
    } catch (err) {
      console.error('Failed to delete plan:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete plan');
      
      // Log failed admin action
      await adminService.logAdminAction({
        adminUserId: user.$id,
        action: 'delete_store_plan',
        targetType: 'plan',
        targetId: plan.$id,
        details: `Failed to delete plan: ${err instanceof Error ? err.message : 'Unknown error'}`,
        success: false
      });

      toast.showError('Failed to delete plan', err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setActionLoading(null);
    }
  };

  // Handle edit plan
  const handleEditPlan = (plan: StorePlan) => {
    setFormData({
      name: plan.name,
      description: plan.description,
      imageStock: plan.imageStock,
      creditCost: plan.creditCost,
      isActive: plan.isActive,
      features: plan.features || [],
      popularTag: plan.popularTag || false
    });
    setEditingPlan(plan);
    setShowCreateForm(true);
  };

  // Add feature
  const addFeature = () => {
    if (featureInput.trim() && !formData.features.includes(featureInput.trim())) {
      setFormData({
        ...formData,
        features: [...formData.features, featureInput.trim()]
      });
      setFeatureInput('');
    }
  };

  // Remove feature
  const removeFeature = (index: number) => {
    setFormData({
      ...formData,
      features: formData.features.filter((_, i) => i !== index)
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading store plans...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
              🏪 Store Management
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
              Create and manage subscription plans for your users
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
            <Button
              variant="outline"
              onClick={() => window.location.href = '/admin/coupons'}
              className="w-full sm:w-auto"
            >
              Manage Coupons
            </Button>
            <Button
              variant="primary"
              onClick={() => setShowCreateForm(true)}
              className="w-full sm:w-auto"
            >
              Create New Plan
            </Button>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Create/Edit Form */}
        {showCreateForm && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-3 sm:space-y-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {editingPlan ? 'Edit Plan' : 'Create New Plan'}
              </h3>
              <Button
                variant="ghost"
                onClick={resetForm}
                className="self-end sm:self-auto"
              >
                ✕
              </Button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <div className="sm:col-span-2 lg:col-span-1">
                  <Input
                    label="Plan Name"
                    type="text"
                    placeholder="e.g., Pro Plan"
                    value={formData.name}
                    onChange={(value) => setFormData({ ...formData, name: value })}
                    required
                  />
                </div>

                <Input
                  label="Credit Cost"
                  type="number"
                  placeholder="100"
                  value={formData.creditCost.toString()}
                  onChange={(value) => setFormData({ ...formData, creditCost: parseInt(value) || 0 })}
                  required
                />

                <Input
                  label="Additional Image Uploads"
                  type="number"
                  placeholder="50"
                  value={formData.imageStock.toString()}
                  onChange={(value) => setFormData({ ...formData, imageStock: parseInt(value) || 0 })}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500"
                  rows={3}
                  placeholder="Describe the benefits of this plan..."
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  required
                />
              </div>

              {/* Features */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Features
                </label>
                <div className="flex space-x-2 mb-3">
                  <Input
                    type="text"
                    placeholder="Add a feature..."
                    value={featureInput}
                    onChange={setFeatureInput}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addFeature}
                    disabled={!featureInput.trim()}
                  >
                    Add
                  </Button>
                </div>
                
                {formData.features.length > 0 && (
                  <div className="space-y-2">
                    {formData.features.map((feature, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700/50 rounded">
                        <span className="text-sm text-gray-900 dark:text-white">{feature}</span>
                        <button
                          type="button"
                          onClick={() => removeFeature(index)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        >
                          ✕
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Checkboxes */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="w-4 h-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Active Plan</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formData.popularTag}
                    onChange={(e) => setFormData({ ...formData, popularTag: e.target.checked })}
                    className="w-4 h-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">Mark as Popular</span>
                </label>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={resetForm}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={actionLoading === (editingPlan?.$id || 'create')}
                  loading={actionLoading === (editingPlan?.$id || 'create')}
                >
                  {editingPlan ? 'Update Plan' : 'Create Plan'}
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Plans List */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Store Plans ({plans.length})
            </h3>
          </div>

          {plans.length === 0 ? (
            <div className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Plans Created
              </h4>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Create your first store plan to start selling subscriptions.
              </p>
              <Button
                variant="primary"
                onClick={() => setShowCreateForm(true)}
              >
                Create First Plan
              </Button>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {plans.map((plan) => (
                <div key={plan.$id} className="p-4 sm:p-6">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-2">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {plan.name}
                        </h4>
                        
                        {plan.popularTag && (
                          <span className="bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-200 text-xs font-medium px-2 py-1 rounded-full">
                            Popular
                          </span>
                        )}
                        
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                          plan.isActive 
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200'
                            : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'
                        }`}>
                          {plan.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      
                      <p className="text-gray-600 dark:text-gray-400 mb-3 text-sm sm:text-base">
                        {plan.description}
                      </p>
                      
                      <div className="flex flex-wrap items-center gap-4 sm:gap-6 text-sm text-gray-500 dark:text-gray-400">
                        <span>
                          <strong className="text-gray-900 dark:text-white">{formatCurrency(plan.creditCost)}</strong> credits
                        </span>
                        <span>
                          <strong className="text-gray-900 dark:text-white">+{plan.imageStock}</strong> image uploads
                        </span>
                        {plan.features && plan.features.length > 0 && (
                          <span>
                            <strong className="text-gray-900 dark:text-white">{plan.features.length}</strong> features
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditPlan(plan)}
                        disabled={actionLoading === plan.$id}
                        className="w-full sm:w-auto"
                      >
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeletePlan(plan)}
                        disabled={actionLoading === plan.$id}
                        loading={actionLoading === plan.$id}
                        className="w-full sm:w-auto text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/20"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
      </div>
    </DashboardLayout>
  );
}
