'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { adminService } from '@/lib/services/adminService';
import { storeService } from '@/lib/services/storeService';
import { Order, AuthUser } from '@/types';
import { formatCurrency } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProtectedAdminRoute from '@/components/auth/ProtectedAdminRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { ToastContainer, useToast } from '@/components/ui/Toast';

type TabType = 'overview' | 'orders' | 'transactions' | 'info';

export default function AdminUserManagePage() {
  return (
    <ProtectedAdminRoute>
      <AdminUserManageContent />
    </ProtectedAdminRoute>
  );
}

function AdminUserManageContent() {
  const { user: adminUser } = useAuth();
  const { id: userId } = useParams();
  const toast = useToast();

  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userOrders, setUserOrders] = useState<Order[]>([]);
  // const [userTransactions, setUserTransactions] = useState<CreditTransaction[]>([]); // TODO: Implement transactions
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Form state for editing
  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    isAdmin: false,
    totalImagesLimit: 0,
    credits: 0
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId || typeof userId !== 'string') return;

      try {
        setIsLoading(true);
        setError(null);

        // Fetch user details
        const userData = await adminService.getUserById(userId);
        setUser(userData);

        // Set form data
        setEditForm({
          name: userData.name,
          email: userData.email,
          isAdmin: userData.isAdmin,
          totalImagesLimit: userData.totalImagesLimit,
          credits: userData.credits
        });

        // Fetch user orders
        const orders = await storeService.getUserOrders(userId);
        setUserOrders(orders);

        // Fetch user transactions (you'll need to implement this)
        // const transactions = await adminService.getUserTransactions(userId);
        // setUserTransactions(transactions);
      } catch (err) {
        console.error('Failed to fetch user data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load user data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const handleSave = async () => {
    if (!user || !adminUser) return;

    try {
      setActionLoading('save');
      
      // Update user
      const updatedUser = await adminService.updateUser(user.$id, {
        name: editForm.name,
        email: editForm.email,
        isAdmin: editForm.isAdmin,
        totalImagesLimit: editForm.totalImagesLimit,
        credits: editForm.credits
      });

      setUser(updatedUser);
      setIsEditing(false);

      // Log admin action
      await adminService.logAdminAction({
        adminUserId: adminUser.$id,
        action: 'update_user',
        targetType: 'user',
        targetId: user.$id,
        details: `Updated user profile: ${editForm.name}`,
        success: true
      });

      toast.showSuccess('User Updated', 'User profile has been successfully updated');
    } catch (err) {
      console.error('Failed to update user:', err);
      toast.showError('Update Failed', err instanceof Error ? err.message : 'Failed to update user');
    } finally {
      setActionLoading(null);
    }
  };

  const handleCancel = () => {
    if (user) {
      setEditForm({
        name: user.name,
        email: user.email,
        isAdmin: user.isAdmin,
        totalImagesLimit: user.totalImagesLimit,
        credits: user.credits
      });
    }
    setIsEditing(false);
  };

  const addCredits = async (amount: number) => {
    if (!user || !adminUser) return;

    try {
      setActionLoading('add-credits');
      
      const newCredits = user.credits + amount;
      await adminService.updateUser(user.$id, { credits: newCredits });
      
      setUser({ ...user, credits: newCredits });
      setEditForm({ ...editForm, credits: newCredits });

      // Log admin action
      await adminService.logAdminAction({
        adminUserId: adminUser.$id,
        action: 'add_credits',
        targetType: 'user',
        targetId: user.$id,
        details: `Added ${amount} credits to user`,
        success: true
      });

      toast.showSuccess('Credits Added', `Successfully added ${amount} credits`);
    } catch (err) {
      console.error('Failed to add credits:', err);
      toast.showError('Failed to Add Credits', err instanceof Error ? err.message : 'Failed to add credits');
    } finally {
      setActionLoading(null);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading user data...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !user) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Error Loading User</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error || 'User not found'}</p>
          <Button variant="primary" onClick={() => window.history.back()}>
            Go Back
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 space-y-4 sm:space-y-0">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <Button
                variant="ghost"
                onClick={() => window.history.back()}
                className="p-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </Button>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                👤 Manage User
              </h1>
            </div>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
              Manage user account and settings
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
            {!isEditing ? (
              <Button
                variant="primary"
                onClick={() => setIsEditing(true)}
                className="w-full sm:w-auto"
              >
                Edit User
              </Button>
            ) : (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  className="flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleSave}
                  loading={actionLoading === 'save'}
                  className="flex-1 sm:flex-none"
                >
                  Save Changes
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* User Info Card */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 mb-6">
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                  {user.name}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-2">{user.email}</p>
                <div className="flex flex-wrap items-center gap-2">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    user.isAdmin 
                      ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                  }`}>
                    {user.isAdmin ? 'Admin' : 'User'}
                  </span>
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                    {formatCurrency(user.credits)} Credits
                  </span>
                  <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200">
                    {user.currentImages || 0}/{user.totalImagesLimit} Images
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {[
                { id: 'overview', name: 'Overview', icon: '📊' },
                { id: 'orders', name: 'Orders', icon: '🛒' },
                { id: 'transactions', name: 'Transactions', icon: '💳' },
                { id: 'info', name: 'User Info', icon: 'ℹ️' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-cyan-500 text-cyan-600 dark:text-cyan-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Credits</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {formatCurrency(user.credits)}
                        </p>
                      </div>
                      <div className="w-10 h-10 bg-cyan-100 dark:bg-cyan-900/30 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-cyan-600 dark:text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Images Used</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {user.currentImages || 0}
                        </p>
                      </div>
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Orders</p>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {userOrders.length}
                        </p>
                      </div>
                      <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                        <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
                  <div className="flex flex-wrap gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addCredits(100)}
                      loading={actionLoading === 'add-credits'}
                    >
                      Add 100 Credits
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addCredits(500)}
                      loading={actionLoading === 'add-credits'}
                    >
                      Add 500 Credits
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => addCredits(1000)}
                      loading={actionLoading === 'add-credits'}
                    >
                      Add 1000 Credits
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Orders Tab */}
            {activeTab === 'orders' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  User Orders ({userOrders.length})
                </h3>
                {userOrders.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400">No orders found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {userOrders.map((order) => (
                      <div key={order.$id} className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              Order #{order.$id.slice(-8)}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {new Date(order.purchaseDate).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-gray-900 dark:text-white">
                              {formatCurrency(order.total)}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {order.items?.length || 0} items
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Transactions Tab */}
            {activeTab === 'transactions' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Credit Transactions
                </h3>
                <div className="text-center py-12">
                  <p className="text-gray-600 dark:text-gray-400">Credit transactions coming soon...</p>
                </div>
              </div>
            )}

            {/* User Info Tab */}
            {activeTab === 'info' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  User Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    label="Full Name"
                    type="text"
                    value={isEditing ? editForm.name : user.name}
                    onChange={(value) => setEditForm({ ...editForm, name: value })}
                    disabled={!isEditing}
                  />
                  
                  <Input
                    label="Email Address"
                    type="email"
                    value={isEditing ? editForm.email : user.email}
                    onChange={(value) => setEditForm({ ...editForm, email: value })}
                    disabled={!isEditing}
                  />
                  
                  <Input
                    label="Credits"
                    type="number"
                    value={isEditing ? editForm.credits.toString() : user.credits.toString()}
                    onChange={(value) => setEditForm({ ...editForm, credits: parseFloat(value) || 0 })}
                    disabled={!isEditing}
                  />
                  
                  <Input
                    label="Image Limit"
                    type="number"
                    value={isEditing ? editForm.totalImagesLimit.toString() : user.totalImagesLimit.toString()}
                    onChange={(value) => setEditForm({ ...editForm, totalImagesLimit: parseInt(value) || 0 })}
                    disabled={!isEditing}
                  />
                </div>

                {isEditing && (
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={editForm.isAdmin}
                        onChange={(e) => setEditForm({ ...editForm, isAdmin: e.target.checked })}
                        className="w-4 h-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500"
                      />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Admin Access
                      </span>
                    </label>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Grant this user administrator privileges
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
      </div>
    </DashboardLayout>
  );
}
