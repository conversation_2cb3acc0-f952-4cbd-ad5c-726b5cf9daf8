'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { adminService } from '@/lib/services/adminService';
import { UserManagement, AuthUser } from '@/types';
import { formatCurrency } from '@/lib/utils';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProtectedAdminRoute from '@/components/auth/ProtectedAdminRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { ToastContainer, useToast } from '@/components/ui/Toast';
import { useUserManagementRefresh } from '@/hooks/useAutoRefresh';
import RefreshStatus from '@/components/ui/RefreshStatus';

interface UserFilters {
  search: string;
  sortBy: 'created' | 'lastActive' | 'credits' | 'uploads';
  sortOrder: 'asc' | 'desc';
  showBanned: boolean;
}

interface UserUpdateModal {
  isOpen: boolean;
  userManagement: UserManagement | null;
  type: 'credits' | 'imageLimit' | 'ban' | null;
}

export default function AdminUsersPage() {
  return (
    <ProtectedAdminRoute>
      <AdminUsersContent />
    </ProtectedAdminRoute>
  );
}

function AdminUsersContent() {
  const { user: currentUser } = useAuth();
  const [userManagements, setUserManagements] = useState<UserManagement[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserManagement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);

  // Filters and search
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    sortBy: 'created',
    sortOrder: 'desc',
    showBanned: false
  });

  // Auto-refresh hook for user management data
  const {
    data: refreshedUsers,
    isRefreshing,
    lastRefresh,
    error: refreshError,
    refresh: manualRefresh,
    refreshCount
  } = useUserManagementRefresh({
    enabled: autoRefreshEnabled,
    interval: 45000, // 45 seconds
    limit: 50,
    offset: 0,
    onError: (error) => {
      console.error('User management refresh failed:', error);
    },
    onRefresh: () => {
      console.log('User management data refreshed successfully');
    }
  });

  // Update modal
  const [updateModal, setUpdateModal] = useState<UserUpdateModal>({
    isOpen: false,
    userManagement: null,
    type: null
  });
  const [updateValue, setUpdateValue] = useState<string>('');

  // Toast
  const toast = useToast();

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const allUsers = await adminService.getAllUsers();
        setUserManagements(allUsers);
      } catch (err) {
        console.error('Failed to fetch users:', err);
        setError(err instanceof Error ? err.message : 'Failed to load users');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  // Use refreshed data if available, otherwise use initial data
  const currentUserManagements = refreshedUsers || userManagements;

  // Filter and sort users
  useEffect(() => {
    let filtered = [...currentUserManagements];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(userManagement => 
        userManagement.user.name?.toLowerCase().includes(searchLower) ||
        userManagement.user.email?.toLowerCase().includes(searchLower)
      );
    }

    // Ban filter - Note: Need to add isBanned to AuthUser type
    // if (!filters.showBanned) {
    //   filtered = filtered.filter(userManagement => !userManagement.user.isBanned);
    // }

    // Sort
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'created':
          // Note: AuthUser doesn't have $createdAt, need to add or use different field
          aValue = a.user.$id; // Temporary fallback
          bValue = b.user.$id;
          break;
        case 'lastActive':
          // Note: Need to add lastActive to AuthUser type
          aValue = a.user.$id; // Temporary fallback
          bValue = b.user.$id;
          break;
        case 'credits':
          aValue = a.user.credits || 0;
          bValue = b.user.credits || 0;
          break;
        case 'uploads':
          aValue = a.stats.currentImages || 0;
          bValue = b.stats.currentImages || 0;
          break;
        default:
          return 0;
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredUsers(filtered);
  }, [currentUserManagements, filters]);

  // Open update modal
  const openUpdateModal = (userManagement: UserManagement, type: 'credits' | 'imageLimit' | 'ban') => {
    setUpdateModal({ isOpen: true, userManagement, type });
    
    if (type === 'credits') {
      setUpdateValue((userManagement.user.credits || 0).toString());
    } else if (type === 'imageLimit') {
      setUpdateValue((userManagement.user.totalImagesLimit || 0).toString());
    } else {
      setUpdateValue('');
    }
  };

  // Close update modal
  const closeUpdateModal = () => {
    setUpdateModal({ isOpen: false, userManagement: null, type: null });
    setUpdateValue('');
  };

  // Handle user update
  const handleUserUpdate = async () => {
    if (!updateModal.userManagement || !currentUser) return;

    try {
      setActionLoading(updateModal.userManagement.user.$id);
      
      if (updateModal.type === 'credits') {
        const newCredits = parseInt(updateValue) || 0;
        const oldCredits = updateModal.userManagement.user.credits || 0;
        const changeAmount = newCredits - oldCredits;
        
        await adminService.updateUserCredits(
          updateModal.userManagement.user.$id, 
          changeAmount,
          'manual_adjustment',
          currentUser.$id
        );
        
        // Log action
        await adminService.logAdminAction({
          adminUserId: currentUser.$id,
          action: 'update_user_credits',
          targetType: 'user',
          targetId: updateModal.userManagement.user.$id,
          details: `Updated credits from ${oldCredits} to ${newCredits}`,
          success: true
        });
        
        // Update local state
        setUserManagements(userManagements.map(um => 
          um.user.$id === updateModal.userManagement!.user.$id 
            ? { ...um, user: { ...um.user, credits: newCredits } }
            : um
        ));
        
        toast.showSuccess(`Credits updated successfully`, `${oldCredits} → ${newCredits}`);
        
      } else if (updateModal.type === 'imageLimit') {
        const newLimit = parseInt(updateValue) || 0;
        const oldLimit = updateModal.userManagement.user.totalImagesLimit || 0;
        
        await adminService.updateUserImageLimit(
          updateModal.userManagement.user.$id, 
          newLimit,
          'admin_update',
          currentUser.$id
        );
        
        // Log action
        await adminService.logAdminAction({
          adminUserId: currentUser.$id,
          action: 'update_user_image_limit',
          targetType: 'user',
          targetId: updateModal.userManagement.user.$id,
          details: `Updated image limit from ${oldLimit} to ${newLimit}`,
          success: true
        });
        
        // Update local state
        setUserManagements(userManagements.map(um => 
          um.user.$id === updateModal.userManagement!.user.$id 
            ? { ...um, user: { ...um.user, totalImagesLimit: newLimit } }
            : um
        ));
        
        toast.showSuccess(`Image limit updated successfully`, `${oldLimit} → ${newLimit}`);
        
      } else if (updateModal.type === 'ban') {
        // Note: Ban functionality would need to be implemented in adminService
        // For now, just log the action
        await adminService.logAdminAction({
          adminUserId: currentUser.$id,
          action: 'ban_user_attempt',
          targetType: 'user',
          targetId: updateModal.userManagement.user.$id,
          details: `Attempted to ban user: ${updateModal.userManagement.user.name || updateModal.userManagement.user.email}`,
          success: false
        });
        
        setError('Ban functionality not yet implemented');
        toast.showWarning('Ban functionality not yet implemented');
      }

      closeUpdateModal();
    } catch (err) {
      console.error('Failed to update user:', err);
      setError(err instanceof Error ? err.message : 'Failed to update user');
      toast.showError('Failed to update user', err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setActionLoading(null);
    }
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading users...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                👥 User Management
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                View and manage all registered users
              </p>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              {filteredUsers.length} of {currentUserManagements.length} users
            </div>
          </div>

          {/* Refresh Status */}
          <RefreshStatus
            isRefreshing={isRefreshing}
            lastRefresh={lastRefresh}
            error={refreshError}
            refreshCount={refreshCount}
            onManualRefresh={manualRefresh}
            onToggleAutoRefresh={setAutoRefreshEnabled}
            autoRefreshEnabled={autoRefreshEnabled}
            showControls={true}
            showStats={true}
            className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-600 dark:text-red-400 text-sm">{error}</p>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              label="Search Users"
              type="text"
              placeholder="Name or email..."
              value={filters.search}
              onChange={(value) => setFilters({ ...filters, search: value })}
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sort By
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters({ ...filters, sortBy: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="created">Join Date</option>
                <option value="lastActive">Last Active</option>
                <option value="credits">Credits</option>
                <option value="uploads">Total Uploads</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Order
              </label>
              <select
                value={filters.sortOrder}
                onChange={(e) => setFilters({ ...filters, sortOrder: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>
            
            <div className="flex items-end">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.showBanned}
                  onChange={(e) => setFilters({ ...filters, showBanned: e.target.checked })}
                  className="w-4 h-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">Show Banned</span>
              </label>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {filteredUsers.length === 0 ? (
            <div className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Users Found
              </h4>
              <p className="text-gray-600 dark:text-gray-400">
                {filters.search ? 'No users match your search criteria.' : 'No users registered yet.'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700/50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Credits
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Image Limit
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Uploads
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredUsers.map((userManagement) => (
                    <tr key={userManagement.user.$id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                            {(userManagement.user.name || userManagement.user.email || '?')[0].toUpperCase()}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {userManagement.user.name || 'Unnamed User'}
                              {userManagement.user.isAdmin && (
                                <span className="ml-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs font-medium px-2 py-1 rounded-full">
                                  Admin
                                </span>
                              )}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {userManagement.user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white font-medium">
                          {formatCurrency(userManagement.user.credits || 0)}
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {userManagement.user.totalImagesLimit || 0} images
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {userManagement.stats.currentImages || 0} used
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {userManagement.stats.currentImages || 0}
                        </div>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                          Active
                        </span>
                      </td>
                      
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center space-x-2 justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openUpdateModal(userManagement, 'credits')}
                            disabled={actionLoading === userManagement.user.$id}
                          >
                            Credits
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openUpdateModal(userManagement, 'imageLimit')}
                            disabled={actionLoading === userManagement.user.$id}
                          >
                            Limit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => openUpdateModal(userManagement, 'ban')}
                            disabled={actionLoading === userManagement.user.$id || userManagement.user.isAdmin}
                            className="text-red-600 border-red-300 hover:bg-red-50"
                          >
                            Ban
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Update Modal */}
        {updateModal.isOpen && updateModal.userManagement && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {updateModal.type === 'credits' && 'Update User Credits'}
                  {updateModal.type === 'imageLimit' && 'Update Image Limit'}
                  {updateModal.type === 'ban' && 'Ban User'}
                </h3>
                
                <div className="mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    User: <strong>{updateModal.userManagement.user.name || updateModal.userManagement.user.email}</strong>
                  </p>
                  
                  {updateModal.type === 'credits' && (
                    <Input
                      label="New Credit Amount"
                      type="number"
                      placeholder="Enter new credit amount"
                      value={updateValue}
                      onChange={setUpdateValue}
                    />
                  )}
                  
                  {updateModal.type === 'imageLimit' && (
                    <Input
                      label="New Image Limit"
                      type="number"
                      placeholder="Enter new image limit"
                      value={updateValue}
                      onChange={setUpdateValue}
                    />
                  )}
                  
                  {updateModal.type === 'ban' && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Ban functionality is not yet implemented. This would prevent the user from accessing the application.
                    </p>
                  )}
                </div>
                
                <div className="flex justify-end space-x-3">
                  <Button
                    variant="outline"
                    onClick={closeUpdateModal}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleUserUpdate}
                    loading={actionLoading === updateModal.userManagement.user.$id}
                    disabled={updateModal.type === 'ban'}
                    className={updateModal.type === 'ban' ? 'bg-red-600 hover:bg-red-700' : ''}
                  >
                    {updateModal.type === 'credits' && 'Update Credits'}
                    {updateModal.type === 'imageLimit' && 'Update Limit'}
                    {updateModal.type === 'ban' && 'Ban User (Coming Soon)'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <ToastContainer toasts={toast.toasts} onClose={toast.removeToast} />
      </div>
    </DashboardLayout>
  );
}
