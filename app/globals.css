@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --primary: #0891b2;
  --primary-foreground: #ffffff;
  --secondary: #374151;
  --secondary-foreground: #f9fafb;
  --accent: #06b6d4;
  --accent-foreground: #ffffff;
  --muted: #1f2937;
  --muted-foreground: #9ca3af;
  --border: #374151;
  --input: #374151;
  --ring: #0891b2;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Light theme override (optional - we're using dark as primary) */
@media (prefers-color-scheme: light) {
  :root {
    --background: #ffffff;
    --foreground: #0f172a;
    --secondary: #f1f5f9;
    --secondary-foreground: #0f172a;
    --muted: #f8fafc;
    --muted-foreground: #64748b;
    --border: #e2e8f0;
    --input: #e2e8f0;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--accent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-gray-900;
}

/* Animation utilities */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes particleFloat {
  0% { transform: translateY(0px) rotate(0deg); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
}

@keyframes slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-gradient {
  animation: gradientShift 8s ease infinite;
  background-size: 200% 200%;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out;
}

/* Background Effects */
.bg-animated-gradient {
  background: linear-gradient(
    -45deg,
    #0f172a,
    #1e293b,
    #0c4a6e,
    #164e63,
    #155e75,
    #0891b2
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

.bg-particles::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(6, 182, 212, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(14, 165, 233, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(6, 182, 212, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(14, 165, 233, 0.4), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(6, 182, 212, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 20s linear infinite;
  z-index: -1;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient text effects */
.gradient-text {
  background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

/* Hover glow effects */
.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow:
    0 0 20px rgba(6, 182, 212, 0.3),
    0 0 40px rgba(6, 182, 212, 0.2),
    0 0 60px rgba(6, 182, 212, 0.1);
  transform: translateY(-2px);
}

/* Card effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(6, 182, 212, 0.1);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-dark {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}
