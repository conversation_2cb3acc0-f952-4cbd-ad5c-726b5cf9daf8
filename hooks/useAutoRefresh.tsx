'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

export interface AutoRefreshOptions {
  interval?: number; // in milliseconds, default 30000 (30 seconds)
  enabled?: boolean; // default true
  onError?: (error: Error) => void;
  onRefresh?: () => void;
}

export interface AutoRefreshState {
  isRefreshing: boolean;
  lastRefresh: Date | null;
  error: Error | null;
  refreshCount: number;
}

/**
 * Hook for auto-refreshing data at specified intervals
 */
export function useAutoRefresh<T>(
  fetchFunction: () => Promise<T>,
  options: AutoRefreshOptions = {}
): AutoRefreshState & {
  data: T | null;
  refresh: () => Promise<void>;
} {
  const {
    interval = 30000,
    enabled = true,
    onError,
    onRefresh
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [refreshCount, setRefreshCount] = useState(0);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  const refresh = useCallback(async () => {
    if (!mountedRef.current) return;
    
    try {
      setIsRefreshing(true);
      setError(null);
      
      const result = await fetchFunction();
      
      if (mountedRef.current) {
        setData(result);
        setLastRefresh(new Date());
        setRefreshCount(prev => prev + 1);
        onRefresh?.();
      }
    } catch (err) {
      if (mountedRef.current) {
        const error = err instanceof Error ? err : new Error('Refresh failed');
        setError(error);
        onError?.(error);
      }
    } finally {
      if (mountedRef.current) {
        setIsRefreshing(false);
      }
    }
  }, [fetchFunction, onError, onRefresh]);

  // Set up auto-refresh interval
  useEffect(() => {
    if (!enabled) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Initial fetch
    refresh();

    // Set up interval
    intervalRef.current = setInterval(refresh, interval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, interval, refresh]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    data,
    isRefreshing,
    lastRefresh,
    error,
    refreshCount,
    refresh
  };
}

/**
 * Specialized hook for user stats refresh
 */
export function useUserStatsRefresh(
  userId: string,
  options: AutoRefreshOptions = {}
) {
  const fetchUserStats = useCallback(async () => {
    const { userService } = await import('@/lib/services/userService');
    return await userService.getUserStats(userId);
  }, [userId]);

  return useAutoRefresh(fetchUserStats, {
    interval: 60000, // 1 minute
    ...options
  });
}

/**
 * Specialized hook for admin logs refresh
 */
export function useAdminLogsRefresh(options: AutoRefreshOptions & {
  limit?: number;
  offset?: number;
  filters?: any;
} = {}) {
  const { limit = 50, offset = 0, filters, ...refreshOptions } = options;
  
  const fetchAdminLogs = useCallback(async () => {
    // Logging service removed
    return [];
  }, [limit, offset, filters]);

  return useAutoRefresh(fetchAdminLogs, {
    interval: 30000, // 30 seconds
    ...refreshOptions
  });
}

/**
 * Specialized hook for admin stats refresh
 */
export function useAdminStatsRefresh(options: AutoRefreshOptions = {}) {
  const fetchAdminStats = useCallback(async () => {
    const { requestManager } = await import('@/lib/services/requestManager');
    return await requestManager.fetchAdminStats();
  }, []);

  return useAutoRefresh(fetchAdminStats, {
    interval: 60000, // 1 minute
    ...options
  });
}

/**
 * Specialized hook for user management refresh
 */
export function useUserManagementRefresh(options: AutoRefreshOptions & {
  limit?: number;
  offset?: number;
} = {}) {
  const { limit = 50, offset = 0, ...refreshOptions } = options;

  const fetchUserManagement = useCallback(async () => {
    const { adminService } = await import('@/lib/services/adminService');
    return await adminService.getAllUsers(limit, offset);
  }, [limit, offset]);

  return useAutoRefresh(fetchUserManagement, {
    interval: 45000, // 45 seconds
    ...refreshOptions
  });
}

/**
 * Specialized hook for orders refresh
 */
export function useOrdersRefresh(options: AutoRefreshOptions & {
  userId?: string;
} = {}) {
  const { userId, ...refreshOptions } = options;

  const fetchOrders = useCallback(async () => {
    const { storeService } = await import('@/lib/services/storeService');
    if (userId) {
      return await storeService.getUserOrders(userId);
    } else {
      return await storeService.getAllOrders();
    }
  }, [userId]);

  return useAutoRefresh(fetchOrders, {
    interval: 60000, // 1 minute
    ...refreshOptions
  });
}

/**
 * Specialized hook for shopping cart refresh
 */
export function useShoppingCartRefresh(
  userId: string,
  options: AutoRefreshOptions = {}
) {
  const fetchShoppingCart = useCallback(async () => {
    const { storeService } = await import('@/lib/services/storeService');
    return await storeService.getCartItems(userId);
  }, [userId]);

  return useAutoRefresh(fetchShoppingCart, {
    interval: 30000, // 30 seconds
    ...options
  });
}

/**
 * Utility function to format last refresh time
 */
export function formatLastRefresh(lastRefresh: Date | null): string {
  if (!lastRefresh) return 'Never';
  
  const now = new Date();
  const diffMs = now.getTime() - lastRefresh.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  
  if (diffSeconds < 60) {
    return `${diffSeconds}s ago`;
  } else if (diffMinutes < 60) {
    return `${diffMinutes}m ago`;
  } else if (diffHours < 24) {
    return `${diffHours}h ago`;
  } else {
    return lastRefresh.toLocaleDateString();
  }
}

/**
 * Refresh indicator component
 */
export interface RefreshIndicatorProps {
  isRefreshing: boolean;
  lastRefresh: Date | null;
  error: Error | null;
  className?: string;
}

export function RefreshIndicator({ 
  isRefreshing, 
  lastRefresh, 
  error, 
  className = '' 
}: RefreshIndicatorProps) {
  if (error) {
    return (
      <div className={`flex items-center space-x-1 text-red-500 text-xs ${className}`}>
        <span>❌</span>
        <span>Refresh failed</span>
      </div>
    );
  }

  if (isRefreshing) {
    return (
      <div className={`flex items-center space-x-1 text-cyan-600 text-xs ${className}`}>
        <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse"></div>
        <span>Refreshing...</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-1 text-gray-500 text-xs ${className}`}>
      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
      <span>Updated {formatLastRefresh(lastRefresh)}</span>
    </div>
  );
}
