'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authService } from '@/lib/appwrite/auth';
import { AuthUser } from '@/types';
import { requestManager } from '@/lib/services/requestManager';

interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  loginWithDiscord: (successURL?: string, failureURL?: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      setIsLoading(true);
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
      
      // Pre-load user data immediately after authentication check
      if (currentUser) {
        requestManager.preloadUserData(currentUser);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithDiscord = async (
    successURL?: string,
    failureURL?: string
  ) => {
    try {
      setIsLoading(true);

      // Set default URLs if not provided
      const defaultSuccessURL = successURL || `${window.location.origin}/dashboard`;
      const defaultFailureURL = failureURL || `${window.location.origin}/auth/login?error=oauth_failed`;

      // This will redirect to Discord, so we don't need to handle the response here
      await authService.loginWithDiscord(defaultSuccessURL, defaultFailureURL);
    } catch (error) {
      console.error('Discord login failed:', error);
      setIsLoading(false);
      throw error;
    }
  };

  const logout = async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      
      // Clear all cached data on logout for security
      requestManager.invalidatePattern('');
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
      
      // Refresh user data in cache
      if (currentUser) {
        requestManager.preloadUserData(currentUser);
      }
    } catch (error) {
      console.error('Refresh user failed:', error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    loginWithDiscord,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
