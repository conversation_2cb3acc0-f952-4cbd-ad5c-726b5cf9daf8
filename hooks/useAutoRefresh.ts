'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

export interface AutoRefreshOptions {
  interval?: number; // in milliseconds, default 30000 (30 seconds)
  enabled?: boolean; // default true
  onError?: (error: Error) => void;
  onRefresh?: () => void;
}

export interface AutoRefreshState {
  isRefreshing: boolean;
  lastRefresh: Date | null;
  error: Error | null;
  refreshCount: number;
}

/**
 * Hook for auto-refreshing data at specified intervals
 * @param refreshFn Function to call for refreshing data
 * @param options Configuration options
 * @returns State and control functions
 */
export function useAutoRefresh<T>(
  refreshFn: () => Promise<T>,
  options: AutoRefreshOptions = {}
) {
  const {
    interval = 30000, // 30 seconds default
    enabled = true,
    onError,
    onRefresh
  } = options;

  const [state, setState] = useState<AutoRefreshState>({
    isRefreshing: false,
    lastRefresh: null,
    error: null,
    refreshCount: 0
  });

  const [data, setData] = useState<T | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Manual refresh function
  const refresh = useCallback(async () => {
    if (!mountedRef.current) return;

    setState(prev => ({ ...prev, isRefreshing: true, error: null }));
    
    try {
      const result = await refreshFn();
      
      if (mountedRef.current) {
        setData(result);
        setState(prev => ({
          ...prev,
          isRefreshing: false,
          lastRefresh: new Date(),
          refreshCount: prev.refreshCount + 1,
          error: null
        }));
        onRefresh?.();
      }
    } catch (error) {
      if (mountedRef.current) {
        const err = error instanceof Error ? error : new Error('Refresh failed');
        setState(prev => ({
          ...prev,
          isRefreshing: false,
          error: err
        }));
        onError?.(err);
      }
    }
  }, [refreshFn, onError, onRefresh]);

  // Start auto-refresh
  const startAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    if (enabled && interval > 0) {
      intervalRef.current = setInterval(refresh, interval);
    }
  }, [enabled, interval, refresh]);

  // Stop auto-refresh
  const stopAutoRefresh = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Initial load and setup auto-refresh
  useEffect(() => {
    refresh(); // Initial load
    startAutoRefresh();

    return () => {
      stopAutoRefresh();
    };
  }, [refresh, startAutoRefresh, stopAutoRefresh]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      stopAutoRefresh();
    };
  }, [stopAutoRefresh]);

  return {
    data,
    ...state,
    refresh,
    startAutoRefresh,
    stopAutoRefresh
  };
}

/**
 * Hook for refreshing user statistics
 */
export function useUserStatsRefresh(userId?: string, options?: AutoRefreshOptions) {
  const { userService } = require('@/lib/services/userService');
  
  return useAutoRefresh(
    async () => {
      if (!userId) throw new Error('User ID required');
      return await userService.getUserStats(userId);
    },
    {
      interval: 60000, // 1 minute for user stats
      enabled: !!userId,
      ...options
    }
  );
}

/**
 * Hook for refreshing admin logs
 */
export function useAdminLogsRefresh(options?: AutoRefreshOptions & { 
  limit?: number; 
  offset?: number; 
  filters?: any;
}) {
  const { loggingService } = require('@/lib/services/loggingService');
  
  const { limit = 50, offset = 0, filters, ...refreshOptions } = options || {};
  
  return useAutoRefresh(
    async () => {
      return await loggingService.getAdminLogs(limit, offset, filters);
    },
    {
      interval: 30000, // 30 seconds for logs
      ...refreshOptions
    }
  );
}

/**
 * Hook for refreshing admin dashboard stats
 */
export function useAdminStatsRefresh(options?: AutoRefreshOptions) {
  const { adminService } = require('@/lib/services/adminService');
  
  return useAutoRefresh(
    async () => {
      return await adminService.getAdminStats();
    },
    {
      interval: 60000, // 1 minute for admin stats
      ...options
    }
  );
}

/**
 * Hook for refreshing user management data
 */
export function useUserManagementRefresh(options?: AutoRefreshOptions & {
  limit?: number;
  offset?: number;
  searchTerm?: string;
}) {
  const { adminService } = require('@/lib/services/adminService');
  
  const { limit = 20, offset = 0, searchTerm, ...refreshOptions } = options || {};
  
  return useAutoRefresh(
    async () => {
      return await adminService.getAllUsers(limit, offset, searchTerm);
    },
    {
      interval: 45000, // 45 seconds for user lists
      ...refreshOptions
    }
  );
}

/**
 * Hook for refreshing store orders
 */
export function useOrdersRefresh(options?: AutoRefreshOptions & {
  limit?: number;
  offset?: number;
}) {
  const { storeService } = require('@/lib/services/storeService');
  
  const { limit = 20, offset = 0, ...refreshOptions } = options || {};
  
  return useAutoRefresh(
    async () => {
      return await storeService.getAllOrders(limit, offset);
    },
    {
      interval: 45000, // 45 seconds for orders
      ...refreshOptions
    }
  );
}

/**
 * Hook for refreshing shopping cart
 */
export function useShoppingCartRefresh(userId?: string, options?: AutoRefreshOptions) {
  const { storeService } = require('@/lib/services/storeService');
  
  return useAutoRefresh(
    async () => {
      if (!userId) throw new Error('User ID required');
      return await storeService.getCartSummary(userId);
    },
    {
      interval: 30000, // 30 seconds for cart
      enabled: !!userId,
      ...options
    }
  );
}

/**
 * Utility function to format last refresh time
 */
export function formatLastRefresh(lastRefresh: Date | null): string {
  if (!lastRefresh) return 'Never';
  
  const now = new Date();
  const diff = now.getTime() - lastRefresh.getTime();
  const seconds = Math.floor(diff / 1000);
  
  if (seconds < 60) return `${seconds}s ago`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
  return lastRefresh.toLocaleTimeString();
}

/**
 * Component for displaying refresh status
 */
export interface RefreshIndicatorProps {
  isRefreshing: boolean;
  lastRefresh: Date | null;
  error: Error | null;
  className?: string;
}

export function RefreshIndicator({ 
  isRefreshing, 
  lastRefresh, 
  error, 
  className = '' 
}: RefreshIndicatorProps) {
  if (error) {
    return (
      <div className={`flex items-center space-x-1 text-red-500 text-xs ${className}`}>
        <span>❌</span>
        <span>Refresh failed</span>
      </div>
    );
  }

  if (isRefreshing) {
    return (
      <div className={`flex items-center space-x-1 text-cyan-500 text-xs ${className}`}>
        <div className="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
        <span>Refreshing...</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center space-x-1 text-gray-500 text-xs ${className}`}>
      <span>🔄</span>
      <span>Updated {formatLastRefresh(lastRefresh)}</span>
    </div>
  );
}
