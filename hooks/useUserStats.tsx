'use client';

import { useState, useEffect } from 'react';
import { UserStats } from '@/types';
import { userService } from '@/lib/services/userService';
import { useAuth } from './useAuth';
import { requestManager } from '@/lib/services/requestManager';

export function useUserStats() {
  const { user, isAuthenticated } = useAuth();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    if (!user || !isAuthenticated) return;

    try {
      setIsLoading(true);
      setError(null);
      
      // Use request manager for cached, fast loading
      const userStats = await requestManager.fetchUserStats(user.$id);
      setStats(userStats);
    } catch (err) {
      console.error('Failed to fetch user stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch user stats');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshStats = async () => {
    if (!user) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Force refresh to get latest data
      const userStats = await requestManager.forceRefresh('userStats', () => 
        userService.getUserStats(user.$id), user.$id);
      setStats(userStats);
    } catch (err) {
      console.error('Failed to refresh user stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh user stats');
    } finally {
      setIsLoading(false);
    }
  };

  const updateCredits = async (amount: number, reason: string = 'Manual update') => {
    if (!user) return;

    try {
      const newCredits = await userService.updateCredits(user.$id, amount);
      if (stats) {
        setStats({ ...stats, credits: newCredits });
      }
      return newCredits;
    } catch (err) {
      console.error('Failed to update credits:', err);
      throw err;
    }
  };

  const canUploadImage = async (): Promise<boolean> => {
    if (!user) return false;
    return await userService.canUploadImage(user.$id);
  };

  const getRemainingUploads = (): number => {
    if (!stats) return 0;
    return Math.max(0, stats.totalImagesLimit - stats.currentImages);
  };

  const getStorageUsagePercentage = (): number => {
    if (!stats) return 0;
    return Math.min(100, (stats.storageUsed / stats.storageLimit) * 100);
  };

  const deductCredits = async (amount: number, reason: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const newCredits = await userService.deductCredits(user.$id, amount, reason);
      if (stats) {
        setStats({ ...stats, credits: newCredits });
      }
      return newCredits;
    } catch (err) {
      console.error('Failed to deduct credits:', err);
      throw err;
    }
  };

  const addCredits = async (amount: number, reason: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const newCredits = await userService.addCredits(user.$id, amount, reason);
      if (stats) {
        setStats({ ...stats, credits: newCredits });
      }
      return newCredits;
    } catch (err) {
      console.error('Failed to add credits:', err);
      throw err;
    }
  };

  // Fetch stats when user changes or component mounts
  useEffect(() => {
    fetchStats();
  }, [user, isAuthenticated]);

  return {
    stats,
    isLoading,
    error,
    refreshStats,
    updateCredits,
    canUploadImage,
    getRemainingUploads,
    getStorageUsagePercentage,
    deductCredits,
    addCredits,
  };
}

export default useUserStats;
