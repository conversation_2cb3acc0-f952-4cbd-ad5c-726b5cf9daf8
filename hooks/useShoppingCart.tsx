'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { storeService } from '@/lib/services/storeService';
import { requestManager } from '@/lib/services/requestManager';
import { CartItem, CartSummary, CheckoutSummary, StorePlan, Coupon, CheckoutData } from '@/types';

export function useShoppingCart() {
  const { user, isAuthenticated } = useAuth();
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [cartSummary, setCartSummary] = useState<CartSummary>({
    items: [],
    totalItems: 0,
    totalCredits: 0,
    totalImageStock: 0,
    subtotal: 0,
    discount: 0,
    total: 0,
    couponCode: undefined,
    couponDetails: null
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Applied coupon state
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);
  const [couponCode, setCouponCode] = useState('');
  const [couponError, setCouponError] = useState<string | null>(null);

  // Fetch cart items
  const fetchCartItems = useCallback(async () => {
    if (!user || !isAuthenticated) {
      setCartItems([]);
      setCartSummary({
        items: [],
        totalItems: 0,
        totalCredits: 0,
        totalImageStock: 0,
        subtotal: 0,
        discount: 0,
        total: 0,
        couponCode: undefined,
        couponDetails: null
      });
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Use request manager for cached cart data
      const items = await requestManager.fetchCartItems(user.$id);
      const summary = await storeService.getCartSummary(user.$id);

      setCartItems(items);
      setCartSummary(summary);
    } catch (err) {
      console.error('Failed to fetch cart items:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch cart items');
    } finally {
      setIsLoading(false);
    }
  }, [user, isAuthenticated]);

  // Add item to cart
  const addToCart = async (planId: string): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);
      await storeService.addToCart(user.$id, planId);
      await fetchCartItems(); // Refresh cart
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add item to cart';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Remove item from cart
  const removeFromCart = async (cartItemId: string): Promise<void> => {
    try {
      setError(null);
      await storeService.removeFromCart(cartItemId);
      await fetchCartItems(); // Refresh cart
      
      // Clear applied coupon if cart becomes empty
      if (cartItems.length === 1) {
        setAppliedCoupon(null);
        setCouponCode('');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove item from cart';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Clear entire cart
  const clearCart = async (): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);
      await storeService.clearCart(user.$id);
      await fetchCartItems(); // Refresh cart
      
      // Clear applied coupon
      setAppliedCoupon(null);
      setCouponCode('');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear cart';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  // Check if plan is in cart
  const isInCart = (planId: string): boolean => {
    return cartItems.some(item => item.planId === planId);
  };

  // Get cart item by plan ID
  const getCartItem = (planId: string): CartItem | undefined => {
    return cartItems.find(item => item.planId === planId);
  };

  // Apply coupon code
  const applyCoupon = async (code: string): Promise<void> => {
    if (!code.trim()) {
      setCouponError('Please enter a coupon code');
      return;
    }

    try {
      setCouponError(null);
      setIsLoading(true);

      const coupon = await storeService.validateCoupon(code.trim().toUpperCase(), cartSummary.totalCredits);
      
      setAppliedCoupon(coupon);
      setCouponCode(code.trim().toUpperCase());
    } catch (err) {
      setCouponError(err instanceof Error ? err.message : 'Invalid coupon code');
      setAppliedCoupon(null);
      setCouponCode('');
    } finally {
      setIsLoading(false);
    }
  };

  // Remove applied coupon
  const removeCoupon = (): void => {
    setAppliedCoupon(null);
    setCouponCode('');
    setCouponError(null);
  };

  // Calculate checkout data with discount
  const getCheckoutData = (): CheckoutSummary => {
    const subtotal = cartSummary.totalCredits;
    let discount = 0;

    if (appliedCoupon) {
      discount = storeService.calculateDiscount(appliedCoupon, subtotal);
    }

    const total = Math.max(0, subtotal - discount);

    return {
      cartItems,
      subtotal,
      discount,
      couponCode: appliedCoupon?.code,
      total,
      totalImageStock: cartSummary.totalImageStock
    };
  };

  // Process checkout
  const processCheckout = async (userCredits: number): Promise<any> => {
    if (!user) throw new Error('User not authenticated');
    if (cartItems.length === 0) throw new Error('Cart is empty');

    const checkoutSummary = getCheckoutData();

    if (userCredits < checkoutSummary.total) {
      throw new Error('Insufficient credits for this purchase');
    }

    try {
      setIsLoading(true);
      setError(null);

      // Create the actual checkout data for the service
      const checkoutData: CheckoutData = {
        userId: user.$id,
        paymentMethod: 'credits',
        couponCode: checkoutSummary.couponCode
      };

      // Process the order
      const order = await storeService.processCheckout(user.$id, checkoutSummary, userCredits);

      // Clear cart after successful checkout
      await clearCart();

      return order;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Checkout failed';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user can afford cart contents
  const canAffordCart = (userCredits: number): boolean => {
    const checkoutData = getCheckoutData();
    return userCredits >= checkoutData.total;
  };

  // Get required credits for current cart
  const getRequiredCredits = (): number => {
    const checkoutData = getCheckoutData();
    return checkoutData.total;
  };

  // Calculate savings from coupon
  const getCouponSavings = (): number => {
    if (!appliedCoupon) return 0;
    return storeService.calculateDiscount(appliedCoupon, cartSummary.totalCredits);
  };

  // Get formatted discount text
  const getDiscountText = (): string => {
    if (!appliedCoupon) return '';
    
    if (appliedCoupon.discountType === 'percentage') {
      return `${appliedCoupon.discountValue}% off`;
    } else {
      return `${appliedCoupon.discountValue} credits off`;
    }
  };

  // Load cart items when user changes or component mounts
  useEffect(() => {
    fetchCartItems();
  }, [fetchCartItems]);

  return {
    // Cart state
    cartItems,
    cartSummary,
    isLoading,
    error,

    // Cart actions
    addToCart,
    removeFromCart,
    clearCart,
    refreshCart: fetchCartItems,

    // Cart utilities
    isInCart,
    getCartItem,

    // Coupon state
    appliedCoupon,
    couponCode,
    couponError,

    // Coupon actions
    applyCoupon,
    removeCoupon,

    // Checkout
    getCheckoutData,
    processCheckout,
    canAffordCart,
    getRequiredCredits,

    // Discount utilities
    getCouponSavings,
    getDiscountText,

    // Computed values
    isEmpty: cartItems.length === 0,
    itemCount: cartSummary.totalItems,
    totalCredits: cartSummary.totalCredits,
    totalImageStock: cartSummary.totalImageStock,
    hasDiscount: appliedCoupon !== null
  };
}

export default useShoppingCart;
