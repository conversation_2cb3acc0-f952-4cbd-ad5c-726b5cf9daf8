'use client';

import React, { useState } from 'react';
import { formatCurrency, formatNumber } from '@/lib/utils';
import { AuthUser } from '@/types';
import { useUserStats } from '@/hooks/useUserStats';
import { useUserStatsRefresh } from '@/hooks/useAutoRefresh';
import { CompactRefreshStatus } from '@/components/ui/RefreshStatus';

interface UserStatsProps {
  user: AuthUser;
  className?: string;
  showRefreshStatus?: boolean;
}

const UserStats: React.FC<UserStatsProps> = ({
  user,
  className = '',
  showRefreshStatus = false,
}) => {
  const { stats, isLoading } = useUserStats();
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);

  // Auto-refresh hook for real-time updates
  const {
    data: refreshedStats,
    isRefreshing,
    lastRefresh,
    error: refreshError,
    refresh: manualRefresh
  } = useUserStatsRefresh(user.$id, {
    enabled: autoRefreshEnabled,
    interval: 60000, // 1 minute
    onError: (error) => console.error('User stats refresh failed:', error)
  });

  // Use refreshed stats, fallback to hook stats, then user data
  const currentStats = refreshedStats || stats;
  const currentImages = currentStats?.currentImages ?? 0;
  const totalImagesLimit = currentStats?.totalImagesLimit ?? user.totalImagesLimit;
  const credits = currentStats?.credits ?? user.credits;

  if (isLoading && !currentStats) {
    return (
      <div className={`hidden md:flex items-center space-x-6 ${className}`}>
        <div className="flex items-center space-x-2 px-3 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
          <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
          <div className="h-4 w-16 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
        </div>
        <div className="flex items-center space-x-2 px-3 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
          <div className="w-5 h-5 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
          <div className="h-4 w-20 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
        </div>
        {showRefreshStatus && (
          <div className="h-4 w-24 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"></div>
        )}
      </div>
    );
  }

  return (
    <div className={`hidden md:flex items-center space-x-6 ${className}`}>
      {/* Images Count */}
      <div className="flex items-center space-x-2 px-3 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
        <div className="w-5 h-5 bg-gradient-to-br from-cyan-500 to-blue-600 rounded flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div className="text-sm">
          <span className="font-semibold text-gray-900 dark:text-white">
            {formatNumber(currentImages)}
          </span>
          <span className="text-gray-500 dark:text-gray-400 mx-1">/</span>
          <span className="text-gray-600 dark:text-gray-300">
            {formatNumber(totalImagesLimit)}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-1 text-xs">
            images
          </span>
        </div>
      </div>

      {/* Credits */}
      <div className="flex items-center space-x-2 px-3 py-2 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md rounded-lg border border-gray-200/50 dark:border-gray-700/50 shadow-sm">
        <div className="w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-600 rounded flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        </div>
        <div className="text-sm">
          <span className="text-gray-500 dark:text-gray-400 text-xs mr-1">
            Credits:
          </span>
          <span className="font-semibold text-gray-900 dark:text-white">
            {formatCurrency(credits)}
          </span>
        </div>
      </div>

      {/* Admin Badge (if user is admin) */}
      {user.isAdmin && (
        <div className="flex items-center space-x-2 px-3 py-2 bg-gradient-to-r from-purple-500/10 to-violet-500/10 dark:from-purple-500/20 dark:to-violet-500/20 rounded-lg border border-purple-200/50 dark:border-purple-700/50 shadow-sm">
          <div className="w-5 h-5 bg-gradient-to-br from-purple-500 to-violet-600 rounded flex items-center justify-center">
            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
          </div>
          <span className="text-xs font-medium text-purple-700 dark:text-purple-300">
            Admin
          </span>
        </div>
      )}

      {/* Refresh Status (if enabled) */}
      {showRefreshStatus && (
        <CompactRefreshStatus
          isRefreshing={isRefreshing}
          lastRefresh={lastRefresh}
          error={refreshError}
          onManualRefresh={manualRefresh}
          className="ml-2"
        />
      )}
    </div>
  );
};

export default UserStats;
