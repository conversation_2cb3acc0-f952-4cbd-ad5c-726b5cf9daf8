'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useUserStats } from '@/hooks/useUserStats';
import { userService } from '@/lib/services/userService';
import Button from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';

export default function DailyCreditsCard() {
  const { user } = useAuth();
  const { refreshStats } = useUserStats();
  const toast = useToast();
  
  const [canClaim, setCanClaim] = useState(false);
  const [timeUntilNext, setTimeUntilNext] = useState<number | null>(null);
  const [isClaiming, setIsClaiming] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user can claim daily credits
  useEffect(() => {
    const checkEligibility = async () => {
      if (!user) return;
      
      try {
        const result = await userService.canClaimDailyCredits(user.$id);
        setCanClaim(result.canClaim);
        setTimeUntilNext(result.timeUntilNext || null);
      } catch (error) {
        console.error('Error checking daily credits eligibility:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkEligibility();
    // Check every minute
    const interval = setInterval(checkEligibility, 60000);
    
    return () => clearInterval(interval);
  }, [user]);

  const handleClaimCredits = async () => {
    if (!user || !canClaim) return;

    try {
      setIsClaiming(true);
      
      const result = await userService.claimDailyCredits(user.$id);
      
      if (result.success) {
        toast.showSuccess('Daily Credits Claimed!', result.message);
        setCanClaim(false);
        setTimeUntilNext(24 * 60 * 60 * 1000); // 24 hours
        await refreshStats();
      } else {
        toast.showError('Claim Failed', result.message);
      }
    } catch (error) {
      console.error('Error claiming daily credits:', error);
      toast.showError('Error', 'Failed to claim daily credits');
    } finally {
      setIsClaiming(false);
    }
  };

  const formatTimeUntilNext = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  if (!user || isLoading) return null;

  return (
    <div className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
            <span className="text-2xl">🎁</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Daily Credits
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Earn 30 credits every 24 hours
            </p>
          </div>
        </div>
        
        <div className="text-right">
          {canClaim ? (
            <Button
              variant="primary"
              size="sm"
              onClick={handleClaimCredits}
              loading={isClaiming}
              className="bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500"
            >
              Claim 30 Credits
            </Button>
          ) : (
            <div className="text-center">
              <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Next claim in:
              </div>
              <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                {timeUntilNext ? formatTimeUntilNext(timeUntilNext) : '--'}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
