'use client';

import React from 'react';
import { RefreshIndicator, formatLastRefresh } from '@/hooks/useAutoRefresh';
import Button from './Button';

export interface RefreshStatusProps {
  isRefreshing: boolean;
  lastRefresh: Date | null;
  error: Error | null;
  refreshCount?: number;
  onManualRefresh?: () => void;
  onToggleAutoRefresh?: (enabled: boolean) => void;
  autoRefreshEnabled?: boolean;
  className?: string;
  showControls?: boolean;
  showStats?: boolean;
}

export default function RefreshStatus({
  isRefreshing,
  lastRefresh,
  error,
  refreshCount = 0,
  onManualRefresh,
  onToggleAutoRefresh,
  autoRefreshEnabled = false,
  className = '',
  showControls = true,
  showStats = false
}: RefreshStatusProps) {
  return (
    <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0 sm:space-x-4 ${className}`}>
      {/* Status Indicator */}
      <div className="flex items-center space-x-4">
        <RefreshIndicator
          isRefreshing={isRefreshing}
          lastRefresh={lastRefresh}
          error={error}
        />
        
        {showStats && refreshCount > 0 && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {refreshCount} refresh{refreshCount !== 1 ? 'es' : ''}
          </div>
        )}
      </div>

      {/* Controls */}
      {showControls && (
        <div className="flex items-center space-x-2">
          {/* Auto-refresh toggle */}
          {onToggleAutoRefresh && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="auto-refresh-toggle"
                checked={autoRefreshEnabled}
                onChange={(e) => onToggleAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-cyan-600 focus:ring-cyan-500"
              />
              <label 
                htmlFor="auto-refresh-toggle" 
                className="text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap"
              >
                Auto-refresh
              </label>
              {autoRefreshEnabled && (
                <div className="flex items-center space-x-1 text-cyan-600">
                  <div className="w-1.5 h-1.5 bg-cyan-600 rounded-full animate-pulse"></div>
                  <span className="text-xs">Live</span>
                </div>
              )}
            </div>
          )}

          {/* Manual refresh button */}
          {onManualRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onManualRefresh}
              loading={isRefreshing}
              className="text-xs px-2 py-1"
            >
              🔄 Refresh
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Compact version for headers and small spaces
 */
export function CompactRefreshStatus({
  isRefreshing,
  lastRefresh,
  error,
  onManualRefresh,
  className = ''
}: Pick<RefreshStatusProps, 'isRefreshing' | 'lastRefresh' | 'error' | 'onManualRefresh' | 'className'>) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <RefreshIndicator
        isRefreshing={isRefreshing}
        lastRefresh={lastRefresh}
        error={error}
      />
      {onManualRefresh && (
        <button
          onClick={onManualRefresh}
          disabled={isRefreshing}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          title="Refresh data"
        >
          <svg 
            className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
            />
          </svg>
        </button>
      )}
    </div>
  );
}

/**
 * Status bar for displaying multiple refresh states
 */
export interface MultiRefreshStatusProps {
  refreshStates: Array<{
    label: string;
    isRefreshing: boolean;
    lastRefresh: Date | null;
    error: Error | null;
  }>;
  className?: string;
}

export function MultiRefreshStatus({ refreshStates, className = '' }: MultiRefreshStatusProps) {
  const hasErrors = refreshStates.some(state => state.error);
  const isAnyRefreshing = refreshStates.some(state => state.isRefreshing);
  
  return (
    <div className={`bg-gray-50 dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          Data Refresh Status
        </h4>
        <div className="flex items-center space-x-2">
          {isAnyRefreshing && (
            <div className="flex items-center space-x-1 text-cyan-600">
              <div className="w-2 h-2 bg-cyan-600 rounded-full animate-pulse"></div>
              <span className="text-xs">Updating</span>
            </div>
          )}
          {hasErrors && (
            <div className="flex items-center space-x-1 text-red-500">
              <span className="text-xs">⚠️ Some updates failed</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="space-y-1">
        {refreshStates.map((state, index) => (
          <div key={index} className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">{state.label}</span>
            <RefreshIndicator
              isRefreshing={state.isRefreshing}
              lastRefresh={state.lastRefresh}
              error={state.error}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Global refresh status for the entire application
 */
export interface GlobalRefreshStatusProps {
  userStats?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  adminLogs?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  adminStats?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  userManagement?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  orders?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  shoppingCart?: { isRefreshing: boolean; lastRefresh: Date | null; error: Error | null };
  className?: string;
}

export function GlobalRefreshStatus({
  userStats,
  adminLogs,
  adminStats,
  userManagement,
  orders,
  shoppingCart,
  className = ''
}: GlobalRefreshStatusProps) {
  const refreshStates = [
    userStats && { label: 'User Stats', ...userStats },
    adminLogs && { label: 'Admin Logs', ...adminLogs },
    adminStats && { label: 'Admin Stats', ...adminStats },
    userManagement && { label: 'User Management', ...userManagement },
    orders && { label: 'Orders', ...orders },
    shoppingCart && { label: 'Shopping Cart', ...shoppingCart }
  ].filter(Boolean) as Array<{
    label: string;
    isRefreshing: boolean;
    lastRefresh: Date | null;
    error: Error | null;
  }>;

  if (refreshStates.length === 0) return null;

  return (
    <MultiRefreshStatus 
      refreshStates={refreshStates}
      className={className}
    />
  );
}
