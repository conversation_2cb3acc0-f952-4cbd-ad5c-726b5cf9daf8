// Performance monitoring and optimization utilities
import React from 'react';

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics = new Map<string, number[]>();
  private isProduction = process.env.NODE_ENV === 'production';

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Start timing an operation
  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration);
    };
  }

  // Record a metric
  private recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }

    // Log slow operations in development
    if (!this.isProduction && duration > 1000) {
      console.warn(`🐌 Slow operation: ${operation} took ${duration.toFixed(2)}ms`);
    }
  }

  // Get performance statistics
  getStats(operation?: string) {
    if (operation) {
      const metrics = this.metrics.get(operation) || [];
      return this.calculateStats(operation, metrics);
    }

    const allStats: Record<string, any> = {};
    for (const [op, metrics] of this.metrics.entries()) {
      allStats[op] = this.calculateStats(op, metrics);
    }
    return allStats;
  }

  private calculateStats(operation: string, metrics: number[]) {
    if (metrics.length === 0) {
      return { operation, count: 0, avg: 0, min: 0, max: 0 };
    }

    const sum = metrics.reduce((a, b) => a + b, 0);
    const avg = sum / metrics.length;
    const min = Math.min(...metrics);
    const max = Math.max(...metrics);

    return {
      operation,
      count: metrics.length,
      avg: Number(avg.toFixed(2)),
      min: Number(min.toFixed(2)),
      max: Number(max.toFixed(2))
    };
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear();
  }
}

// Higher-order component for measuring component render times
export function withPerformanceMonitoring<T extends {}>(
  Component: React.ComponentType<T>,
  name: string
): React.ComponentType<T> {
  const WrappedComponent = (props: T) => {
    const monitor = PerformanceMonitor.getInstance();
    const endTiming = monitor.startTiming(`component_render:${name}`);
    
    React.useEffect(() => {
      endTiming();
    });

    return React.createElement(Component, props);
  };

  WrappedComponent.displayName = `withPerformanceMonitoring(${name})`;
  return WrappedComponent;
}

// Hook for monitoring async operations
export function usePerformanceMonitor() {
  const monitor = PerformanceMonitor.getInstance();

  const measureAsync = React.useCallback(async <T>(
    operation: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    const endTiming = monitor.startTiming(operation);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      endTiming();
    }
  }, [monitor]);

  const getStats = React.useCallback((operation?: string) => {
    return monitor.getStats(operation);
  }, [monitor]);

  return { measureAsync, getStats };
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();
