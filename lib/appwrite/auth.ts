import { account } from './config';
import { OAuthProvider } from 'appwrite';
import { AuthUser, DEFAULT_USER_CONFIG } from '@/types';
import { userService } from '@/lib/services/userService';

export class AuthService {
  // Initiate Discord OAuth login
  async loginWithDiscord(successURL: string, failureURL: string): Promise<void> {
    try {
      // This will redirect the user to Discord for authentication
      account.createOAuth2Session(OAuthProvider.Discord, successURL, failureURL);
    } catch (error: any) {
      console.error('Discord OAuth error:', error);
      throw new Error(error.message || 'Discord authentication failed');
    }
  }

  // Get current user with Discord profile information
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const user = await account.get();

      // Get user identities to fetch Discord profile data
      let avatarUrl: string | undefined;
      let discordId: string | undefined;

      try {
        const identities = await account.listIdentities();
        const discordIdentity = identities.identities.find(
          (identity: any) => identity.provider === 'discord'
        );

        if (discordIdentity) {
          discordId = discordIdentity.providerUid;

          // Fetch Discord user data to get the actual avatar hash
          if (discordIdentity.providerAccessToken) {
            try {
              const discordResponse = await fetch('https://discord.com/api/users/@me', {
                headers: {
                  'Authorization': `Bearer ${discordIdentity.providerAccessToken}`,
                },
              });

              if (discordResponse.ok) {
                const discordUser = await discordResponse.json();

                // Construct proper Discord avatar URL with actual avatar hash
                if (discordUser.avatar) {
                  avatarUrl = `https://cdn.discordapp.com/avatars/${discordId}/${discordUser.avatar}.png?size=128`;
                } else {
                  // Use default Discord avatar if user has no custom avatar
                  const defaultAvatarIndex = parseInt(discordUser.discriminator) % 5;
                  avatarUrl = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarIndex}.png`;
                }
              }
            } catch (discordError) {
              console.warn('Could not fetch Discord user data:', discordError);
              // Fallback to default Discord avatar
              const defaultAvatarIndex = parseInt(discordId || '0') % 5;
              avatarUrl = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarIndex}.png`;
            }
          }
        }
      } catch (identityError) {
        console.warn('Could not fetch user identities:', identityError);
      }

      // Ensure user exists in database and get configuration
      let userConfig;
      try {
        userConfig = await userService.ensureUserExists(user.$id, {
          name: user.name,
          email: user.email
        });
      } catch (error) {
        console.warn('Could not ensure user exists, using defaults:', error);
        userConfig = {
          isAdmin: DEFAULT_USER_CONFIG.isAdmin,
          totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
          credits: DEFAULT_USER_CONFIG.credits,
        };
      }

      console.log('Discord avatar URL generated:', avatarUrl);

      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
        avatarUrl,
        discordId,
        isAdmin: userConfig.isAdmin,
        totalImagesLimit: userConfig.totalImagesLimit,
        credits: userConfig.credits,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Track login event and send notifications
  async trackLoginEvent(user: AuthUser, request?: Request): Promise<void> {
    try {
      // Get client information
      const userAgent = request?.headers.get('user-agent') || 'Unknown';
      const ipAddress = this.getClientIP(request) || 'Unknown';
      const timestamp = new Date().toISOString();

      // Check if this is a new device (simplified check based on user agent)
      const isNewDevice = await this.isNewDevice(user.$id, userAgent);

      // Log the login event
      try {
        const { loggingService } = await import('../services/loggingService');
        await loggingService.logSystemEvent({
          type: 'info',
          action: 'user_login',
          details: `User ${user.name} logged in via Discord OAuth`,
          metadata: {
            userId: user.$id,
            userEmail: user.email,
            ipAddress,
            userAgent,
            timestamp,
            isNewDevice,
            loginMethod: 'discord_oauth'
          },
          severity: 'low'
        });
      } catch (logError) {
        console.warn('Failed to log login event:', logError);
      }

      // Send login notification email
      try {
        const { emailService } = await import('../services/emailService');
        await emailService.sendLoginNotification(user, {
          timestamp,
          ipAddress,
          userAgent,
          isNewDevice
        });
      } catch (emailError) {
        console.warn('Failed to send login notification email:', emailError);
      }

      // Store device fingerprint for future new device detection
      if (isNewDevice) {
        await this.storeDeviceFingerprint(user.$id, userAgent, ipAddress);
      }

    } catch (error) {
      console.error('Error tracking login event:', error);
      // Don't throw error to avoid disrupting login flow
    }
  }

  // Get client IP address from request
  private getClientIP(request?: Request): string | null {
    if (!request) return null;

    // Check various headers for IP address
    const forwarded = request.headers.get('x-forwarded-for');
    const realIP = request.headers.get('x-real-ip');
    const cfConnectingIP = request.headers.get('cf-connecting-ip');

    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    if (realIP) {
      return realIP;
    }
    if (cfConnectingIP) {
      return cfConnectingIP;
    }

    return null;
  }

  // Check if this is a new device (simplified implementation)
  private async isNewDevice(userId: string, userAgent: string): Promise<boolean> {
    try {
      // In a real implementation, you'd store device fingerprints in the database
      // For now, we'll use a simple localStorage-like approach or always consider it new
      // This is a simplified version - in production you'd want more sophisticated device tracking

      const deviceFingerprint = this.generateDeviceFingerprint(userAgent);

      // Check if we've seen this device before (this would be stored in database)
      // For now, we'll consider any login from a different user agent as potentially new
      // You could enhance this by storing device fingerprints in Appwrite database

      return true; // Simplified: always send notification for now
    } catch (error) {
      console.error('Error checking device status:', error);
      return false;
    }
  }

  // Generate a simple device fingerprint
  private generateDeviceFingerprint(userAgent: string): string {
    // Simple hash of user agent - in production you'd want more sophisticated fingerprinting
    let hash = 0;
    for (let i = 0; i < userAgent.length; i++) {
      const char = userAgent.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  // Store device fingerprint for future reference
  private async storeDeviceFingerprint(userId: string, userAgent: string, ipAddress: string): Promise<void> {
    try {
      // In a real implementation, you'd store this in Appwrite database
      // For now, we'll just log it
      console.log(`New device detected for user ${userId}: ${this.generateDeviceFingerprint(userAgent)}`);

      // TODO: Store in database for future new device detection
      // You could create a "user_devices" collection in Appwrite to track this
    } catch (error) {
      console.error('Error storing device fingerprint:', error);
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      // Log logout event
      try {
        const user = await this.getCurrentUser();
        if (user) {
          const { loggingService } = await import('../services/loggingService');
          await loggingService.logSystemEvent({
            type: 'info',
            action: 'user_logout',
            details: `User ${user.name} logged out`,
            metadata: {
              userId: user.$id,
              userEmail: user.email,
              timestamp: new Date().toISOString()
            },
            severity: 'low'
          });
        }
      } catch (logError) {
        console.warn('Failed to log logout event:', logError);
      }

      await account.deleteSession('current');
    } catch (error: any) {
      console.error('Logout error:', error);
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get user sessions
  async getSessions() {
    try {
      return await account.listSessions();
    } catch (error: any) {
      console.error('Get sessions error:', error);
      throw new Error(error.message || 'Failed to get sessions');
    }
  }

  // Delete all sessions (logout from all devices)
  async logoutFromAllDevices(): Promise<void> {
    try {
      await account.deleteSessions();
    } catch (error: any) {
      console.error('Logout from all devices error:', error);
      throw new Error(error.message || 'Failed to logout from all devices');
    }
  }

  // Update user name (for OAuth users)
  async updateName(name: string): Promise<AuthUser> {
    try {
      const user = await account.updateName(name);
      // Get the current user data to preserve configuration
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        throw new Error('Failed to get current user data');
      }

      return {
        ...currentUser,
        name: user.name,
      };
    } catch (error: any) {
      console.error('Update name error:', error);
      throw new Error(error.message || 'Failed to update name');
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
