import { account } from './config';
import { OAuthProvider } from 'appwrite';
import { AuthUser, DEFAULT_USER_CONFIG } from '@/types';
import { userService } from '@/lib/services/userService';

export class AuthService {
  // Initiate Discord OAuth login
  async loginWithDiscord(successURL: string, failureURL: string): Promise<void> {
    try {
      // This will redirect the user to Discord for authentication
      account.createOAuth2Session(OAuthProvider.Discord, successURL, failureURL);
    } catch (error: any) {
      console.error('Discord OAuth error:', error);
      throw new Error(error.message || 'Discord authentication failed');
    }
  }

  // Get current user with Discord profile information
  async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const user = await account.get();

      // Get user identities to fetch Discord profile data
      let avatarUrl: string | undefined;
      let discordId: string | undefined;

      try {
        const identities = await account.listIdentities();
        const discordIdentity = identities.identities.find(
          (identity: any) => identity.provider === 'discord'
        );

        if (discordIdentity) {
          discordId = discordIdentity.providerUid;

          // Fetch Discord user data to get the actual avatar hash
          if (discordIdentity.providerAccessToken) {
            try {
              const discordResponse = await fetch('https://discord.com/api/users/@me', {
                headers: {
                  'Authorization': `Bearer ${discordIdentity.providerAccessToken}`,
                },
              });

              if (discordResponse.ok) {
                const discordUser = await discordResponse.json();

                // Construct proper Discord avatar URL with actual avatar hash
                if (discordUser.avatar) {
                  avatarUrl = `https://cdn.discordapp.com/avatars/${discordId}/${discordUser.avatar}.png?size=128`;
                } else {
                  // Use default Discord avatar if user has no custom avatar
                  const defaultAvatarIndex = parseInt(discordUser.discriminator) % 5;
                  avatarUrl = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarIndex}.png`;
                }
              }
            } catch (discordError) {
              console.warn('Could not fetch Discord user data:', discordError);
              // Fallback to default Discord avatar
              const defaultAvatarIndex = parseInt(discordId || '0') % 5;
              avatarUrl = `https://cdn.discordapp.com/embed/avatars/${defaultAvatarIndex}.png`;
            }
          }
        }
      } catch (identityError) {
        console.warn('Could not fetch user identities:', identityError);
      }

      // Ensure user exists in database and get configuration
      let userConfig;
      try {
        userConfig = await userService.ensureUserExists(user.$id, {
          name: user.name,
          email: user.email
        });
      } catch (error) {
        console.warn('Could not ensure user exists, using defaults:', error);
        userConfig = {
          isAdmin: DEFAULT_USER_CONFIG.isAdmin,
          totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
          credits: DEFAULT_USER_CONFIG.credits,
        };
      }

      console.log('Discord avatar URL generated:', avatarUrl);

      return {
        $id: user.$id,
        name: user.name,
        email: user.email,
        avatarUrl,
        discordId,
        isAdmin: userConfig.isAdmin,
        totalImagesLimit: userConfig.totalImagesLimit,
        credits: userConfig.credits,
      };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }



  // Logout user
  async logout(): Promise<void> {
    try {
      await account.deleteSession('current');
    } catch (error: any) {
      console.error('Logout error:', error);
      throw new Error(error.message || 'Logout failed');
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      await account.get();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Get user sessions
  async getSessions() {
    try {
      return await account.listSessions();
    } catch (error: any) {
      console.error('Get sessions error:', error);
      throw new Error(error.message || 'Failed to get sessions');
    }
  }

  // Delete all sessions (logout from all devices)
  async logoutFromAllDevices(): Promise<void> {
    try {
      await account.deleteSessions();
    } catch (error: any) {
      console.error('Logout from all devices error:', error);
      throw new Error(error.message || 'Failed to logout from all devices');
    }
  }

  // Update user name (for OAuth users)
  async updateName(name: string): Promise<AuthUser> {
    try {
      const user = await account.updateName(name);
      // Get the current user data to preserve configuration
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        throw new Error('Failed to get current user data');
      }

      return {
        ...currentUser,
        name: user.name,
      };
    } catch (error: any) {
      console.error('Update name error:', error);
      throw new Error(error.message || 'Failed to update name');
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
