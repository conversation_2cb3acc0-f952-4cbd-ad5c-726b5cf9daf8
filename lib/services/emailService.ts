// Complete Email Service with SMTP Configuration and Templates
// Supports order confirmations, welcome emails, credit notifications, and admin alerts

import { Order, OrderItem, AuthUser } from '@/types';
import { loggingService } from './loggingService';
import nodemailer from 'nodemailer';

export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  from?: string;
  replyTo?: string;
}

export interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    name: string;
    email: string;
  };
}

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export type EmailType = 'order_confirmation' | 'welcome' | 'credit_transaction' | 'admin_alert' | 'password_reset' | 'account_verification';

export class EmailService {
  private static instance: EmailService;
  private smtpConfig: SMTPConfig | null = null;

  constructor() {
    this.initializeSMTPConfig();
  }

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  private initializeSMTPConfig(): void {
    // Initialize SMTP configuration from environment variables
    const host = process.env.SMTP_HOST;
    const port = process.env.SMTP_PORT;
    const user = process.env.SMTP_USER;
    const pass = process.env.SMTP_PASS;
    const fromName = process.env.SMTP_FROM_NAME || 'AveImgCloud';
    const fromEmail = process.env.SMTP_FROM_EMAIL || '<EMAIL>';

    if (host && port && user && pass) {
      this.smtpConfig = {
        host,
        port: parseInt(port),
        secure: parseInt(port) === 465, // true for 465, false for other ports
        auth: {
          user,
          pass
        },
        from: {
          name: fromName,
          email: fromEmail
        }
      };
      console.log('SMTP configuration initialized successfully');
    } else {
      console.warn('SMTP configuration incomplete. Email sending will be simulated.');
    }
  }

  public getSMTPConfig(): SMTPConfig | null {
    return this.smtpConfig;
  }

  public updateSMTPConfig(config: SMTPConfig): void {
    this.smtpConfig = config;
    console.log('SMTP configuration updated');
  }

  // Generate welcome email for new Discord OAuth users
  generateWelcomeEmail(user: AuthUser): EmailTemplate {
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to AveImgCloud!</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #0f172a;
            color: #e2e8f0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
          }
          .header {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          .content {
            padding: 2.5rem 2rem;
          }
          .welcome-card {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 2rem;
            margin: 1.5rem 0;
            text-align: center;
          }
          .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
          }
          .stat-card {
            background: #0891b2;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
          }
          .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
          }
          .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
          }
          .feature-list {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
          }
          .feature-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #334155;
            display: flex;
            align-items: center;
          }
          .feature-list li:last-child {
            border-bottom: none;
          }
          .feature-icon {
            margin-right: 1rem;
            font-size: 1.25rem;
          }
          .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 1.5rem 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          }
          .footer {
            background: #0f172a;
            color: #64748b;
            padding: 2rem;
            text-align: center;
            font-size: 0.875rem;
          }
          .footer a {
            color: #0891b2;
            text-decoration: none;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to AveImgCloud!</h1>
            <p>The ultimate image hosting platform for free</p>
          </div>

          <div class="content">
            <div class="welcome-card">
              <h2 style="color: #06b6d4; margin-top: 0;">Hello ${user.name}! 👋</h2>
              <p>
                Thank you for joining AveImgCloud through Discord! Your account has been
                successfully created and you're ready to start hosting images like a pro.
              </p>
            </div>

            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">${user.totalImagesLimit}</div>
                <div class="stat-label">Image Uploads</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">$${user.credits.toFixed(2)}</div>
                <div class="stat-label">Starting Credits</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">100MB</div>
                <div class="stat-label">File Size Limit</div>
              </div>
            </div>

            <h3 style="color: #06b6d4;">🚀 What you can do now:</h3>
            <ul class="feature-list">
              <li>
                <span class="feature-icon">📸</span>
                Upload images with custom embed titles, footers, and colors
              </li>
              <li>
                <span class="feature-icon">🔗</span>
                Get beautiful UUID-format URLs (/img/xxxxx-xxxxx-xxxxx-xxxxx)
              </li>
              <li>
                <span class="feature-icon">🎨</span>
                Rich embeds for Discord and Twitter with Open Graph meta tags
              </li>
              <li>
                <span class="feature-icon">💎</span>
                Earn daily credits and purchase additional image slots
              </li>
              <li>
                <span class="feature-icon">⚡</span>
                Lightning-fast CDN delivery worldwide
              </li>
            </ul>

            <div style="text-align: center;">
              <a href="https://aveimgcloud.com/dashboard" class="cta-button">
                🚀 Start Uploading Images
              </a>
            </div>

            <p style="margin-top: 2rem;">
              Need help getting started? Check out our
              <a href="https://aveimgcloud.com/docs" style="color: #0891b2;">documentation</a>
              or <a href="mailto:<EMAIL>" style="color: #0891b2;">contact support</a>.
            </p>
          </div>

          <div class="footer">
            <p>
              This email was sent to ${user.email}<br>
              <a href="https://aveimgcloud.com">AveImgCloud</a> |
              <a href="https://aveimgcloud.com/terms">Terms of Service</a> |
              <a href="https://aveimgcloud.com/privacy">Privacy Policy</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    return {
      to: user.email,
      subject: '🎉 Welcome to AveImgCloud - Your Account is Ready!',
      html: html.trim(),
      from: this.smtpConfig?.from.email,
      replyTo: '<EMAIL>'
    };
  }

  // Generate purchase confirmation email HTML
  generatePurchaseConfirmationEmail(
    userEmail: string,
    userName: string,
    order: Order
  ): EmailTemplate {
    const totalImageStock = order.items.reduce((total, item) => {
      return total + (item.imageStock * item.quantity);
    }, 0);

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Purchase Confirmation - AveImgCloud</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            color: white;
            padding: 2rem;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
          }
          .content {
            padding: 2rem;
          }
          .order-details {
            background: #f1f5f9;
            border-radius: 6px;
            padding: 1.5rem;
            margin: 1.5rem 0;
          }
          .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e2e8f0;
          }
          .order-item:last-child {
            border-bottom: none;
          }
          .item-name {
            font-weight: 500;
            color: #1e293b;
          }
          .item-details {
            color: #64748b;
            font-size: 0.875rem;
          }
          .item-value {
            font-weight: 600;
            color: #0891b2;
          }
          .total-section {
            background: #0891b2;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 6px;
            margin: 1.5rem 0;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .footer {
            background: #1e293b;
            color: #94a3b8;
            padding: 1.5rem 2rem;
            text-align: center;
            font-size: 0.875rem;
          }
          .footer a {
            color: #0891b2;
            text-decoration: none;
          }
          .highlight {
            background: #dbeafe;
            color: #1e40af;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            border-left: 4px solid #3b82f6;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Purchase Confirmed!</h1>
            <p>Thank you for your purchase, ${userName}!</p>
          </div>
          
          <div class="content">
            <p>Your order has been processed successfully. Here are the details:</p>
            
            <div class="order-details">
              <h3 style="margin-top: 0; color: #1e293b;">Order #${order.orderNumber}</h3>
              <p style="color: #64748b; margin-bottom: 1rem;">
                Purchase Date: ${new Date(order.purchaseDate).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
              
              ${order.items.map(item => `
                <div class="order-item">
                  <div>
                    <div class="item-name">${item.planName}</div>
                    <div class="item-details">
                      ${item.imageStock} images × ${item.quantity} 
                      ${item.quantity > 1 ? 'plans' : 'plan'}
                    </div>
                  </div>
                  <div class="item-value">
                    ${item.creditCost * item.quantity} credits
                  </div>
                </div>
              `).join('')}
            </div>
            
            <div class="total-section">
              <div class="total-row">
                <span>Total Credits Used:</span>
                <span style="font-size: 1.25rem; font-weight: 700;">
                  ${order.totalCredits} credits
                </span>
              </div>
              ${order.discountAmount > 0 ? `
                <div class="total-row" style="margin-top: 0.5rem; font-size: 0.875rem;">
                  <span>Discount Applied:</span>
                  <span>-${order.discountAmount} credits</span>
                </div>
              ` : ''}
            </div>
            
            <div class="highlight">
              <strong>🖼️ Image Limit Increased!</strong><br>
              Your account now has ${totalImageStock} additional image uploads available.
            </div>
            
            <p>
              You can start uploading images immediately. Your new image allowance has been 
              automatically added to your account.
            </p>
            
            <p>
              If you have any questions or need assistance, please don't hesitate to 
              <a href="mailto:<EMAIL>" style="color: #0891b2;">contact our support team</a>.
            </p>
            
            <p>Thank you for choosing AveImgCloud!</p>
          </div>
          
          <div class="footer">
            <p>
              This email was sent to ${userEmail}<br>
              <a href="https://aveimgcloud.com">AveImgCloud</a> | 
              <a href="https://aveimgcloud.com/terms">Terms of Service</a> | 
              <a href="https://aveimgcloud.com/privacy">Privacy Policy</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    return {
      to: userEmail,
      subject: `Purchase Confirmed - Order #${order.orderNumber} | AveImgCloud`,
      html: html.trim(),
      from: this.smtpConfig?.from.email,
      replyTo: '<EMAIL>'
    };
  }

  // Generate credit transaction notification email
  generateCreditTransactionEmail(
    userEmail: string,
    userName: string,
    transactionType: 'earned' | 'spent' | 'bonus',
    amount: number,
    description: string,
    newBalance: number
  ): EmailTemplate {
    const isPositive = transactionType === 'earned' || transactionType === 'bonus';
    const emoji = isPositive ? '💰' : '💳';
    const color = isPositive ? '#10b981' : '#f59e0b';
    const actionText = isPositive ? 'credited to' : 'debited from';

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Credit Transaction - AveImgCloud</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #0f172a;
            color: #e2e8f0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #1e293b;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
          }
          .header {
            background: linear-gradient(135deg, ${color} 0%, ${color}dd 100%);
            color: white;
            padding: 2rem;
            text-align: center;
          }
          .content {
            padding: 2rem;
          }
          .transaction-card {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 2rem;
            margin: 1.5rem 0;
            text-align: center;
          }
          .amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: ${color};
            margin: 1rem 0;
          }
          .balance-card {
            background: #0891b2;
            color: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            margin: 1.5rem 0;
          }
          .footer {
            background: #0f172a;
            color: #64748b;
            padding: 1.5rem 2rem;
            text-align: center;
            font-size: 0.875rem;
          }
          .footer a {
            color: #0891b2;
            text-decoration: none;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${emoji} Credit Transaction</h1>
            <p>Your AveImgCloud account has been updated</p>
          </div>

          <div class="content">
            <p>Hello ${userName},</p>

            <div class="transaction-card">
              <h3 style="color: #06b6d4; margin-top: 0;">Transaction Details</h3>
              <div class="amount">${isPositive ? '+' : '-'}$${Math.abs(amount).toFixed(2)}</div>
              <p style="margin: 0.5rem 0;">
                <strong>${description}</strong>
              </p>
              <p style="color: #64748b; font-size: 0.875rem;">
                ${Math.abs(amount).toFixed(2)} credits ${actionText} your account
              </p>
            </div>

            <div class="balance-card">
              <h4 style="margin: 0 0 0.5rem 0;">Current Balance</h4>
              <div style="font-size: 1.5rem; font-weight: 700;">
                $${newBalance.toFixed(2)}
              </div>
            </div>

            <p>
              ${isPositive
                ? 'You can use these credits to purchase additional image upload slots in our store.'
                : 'Thank you for your purchase! Your new image slots are now available.'}
            </p>

            <p>
              View your complete transaction history in your
              <a href="https://aveimgcloud.com/dashboard" style="color: #0891b2;">dashboard</a>.
            </p>
          </div>

          <div class="footer">
            <p>
              This email was sent to ${userEmail}<br>
              <a href="https://aveimgcloud.com">AveImgCloud</a> |
              <a href="https://aveimgcloud.com/terms">Terms of Service</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    return {
      to: userEmail,
      subject: `${emoji} Credit ${transactionType === 'earned' ? 'Earned' : transactionType === 'bonus' ? 'Bonus' : 'Transaction'} - AveImgCloud`,
      html: html.trim(),
      from: this.smtpConfig?.from.email,
      replyTo: '<EMAIL>'
    };
  }

  // Generate admin alert email
  generateAdminAlertEmail(
    adminEmail: string,
    alertType: 'error' | 'warning' | 'info' | 'critical',
    title: string,
    message: string,
    details?: Record<string, any>
  ): EmailTemplate {
    const alertConfig = {
      error: { emoji: '🚨', color: '#ef4444', bgColor: '#fef2f2' },
      warning: { emoji: '⚠️', color: '#f59e0b', bgColor: '#fffbeb' },
      info: { emoji: 'ℹ️', color: '#3b82f6', bgColor: '#eff6ff' },
      critical: { emoji: '🔥', color: '#dc2626', bgColor: '#fef2f2' }
    };

    const config = alertConfig[alertType];

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Admin Alert - AveImgCloud</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: ${config.color};
            color: white;
            padding: 2rem;
            text-align: center;
          }
          .content {
            padding: 2rem;
          }
          .alert-card {
            background: ${config.bgColor};
            border-left: 4px solid ${config.color};
            padding: 1.5rem;
            margin: 1.5rem 0;
          }
          .details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
          }
          .details-table th,
          .details-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
          }
          .details-table th {
            background: #f9fafb;
            font-weight: 600;
          }
          .footer {
            background: #1e293b;
            color: #94a3b8;
            padding: 1.5rem 2rem;
            text-align: center;
            font-size: 0.875rem;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${config.emoji} Admin Alert</h1>
            <p>AveImgCloud System Notification</p>
          </div>

          <div class="content">
            <div class="alert-card">
              <h3 style="margin-top: 0; color: ${config.color};">${title}</h3>
              <p>${message}</p>
              <p style="color: #6b7280; font-size: 0.875rem;">
                Alert Level: <strong style="color: ${config.color};">${alertType.toUpperCase()}</strong><br>
                Time: ${new Date().toLocaleString()}
              </p>
            </div>

            ${details ? `
              <h4>Additional Details:</h4>
              <table class="details-table">
                ${Object.entries(details).map(([key, value]) => `
                  <tr>
                    <th>${key}</th>
                    <td>${typeof value === 'object' ? JSON.stringify(value, null, 2) : value}</td>
                  </tr>
                `).join('')}
              </table>
            ` : ''}

            <p>
              Please review this alert and take appropriate action if necessary.
              You can view more details in the
              <a href="https://aveimgcloud.com/admin/logs" style="color: ${config.color};">admin logs</a>.
            </p>
          </div>

          <div class="footer">
            <p>
              This alert was sent to ${adminEmail}<br>
              AveImgCloud Admin System
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    return {
      to: adminEmail,
      subject: `${config.emoji} [${alertType.toUpperCase()}] ${title} - AveImgCloud`,
      html: html.trim(),
      from: this.smtpConfig?.from.email,
      replyTo: '<EMAIL>'
    };
  }

  // Send email with comprehensive error handling and logging
  async sendEmail(template: EmailTemplate, emailType: EmailType = 'admin_alert'): Promise<EmailSendResult> {
    const startTime = Date.now();

    try {
      // Validate template
      if (!template.to || !template.subject || !template.html) {
        const error = 'Missing required email fields: to, subject, or html';
        await this.logEmailEvent('error', emailType, template.to, error);
        return { success: false, error };
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(template.to)) {
        const error = `Invalid email address: ${template.to}`;
        await this.logEmailEvent('error', emailType, template.to, error);
        return { success: false, error };
      }

      // Set default from address if not provided
      if (!template.from && this.smtpConfig) {
        template.from = `${this.smtpConfig.from.name} <${this.smtpConfig.from.email}>`;
      }

      // Log email attempt
      await this.logEmailEvent('info', emailType, template.to, 'Email sending initiated');

      // For development/testing, simulate email sending when SMTP is not configured
      if (!this.smtpConfig) {
        console.log('📧 Email Simulation:', {
          to: template.to,
          subject: template.subject,
          from: template.from,
          htmlLength: template.html.length,
          type: emailType
        });

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 500));

        const messageId = `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await this.logEmailEvent('success', emailType, template.to, 'Email sent successfully (simulated)', messageId);

        return {
          success: true,
          messageId
        };
      }

      // Send via API endpoint for production
      const response = await fetch('/api/email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...template,
          emailType,
          timestamp: new Date().toISOString()
        })
      });

      const responseData = await response.json().catch(() => ({}));

      if (response.ok) {
        const duration = Date.now() - startTime;
        const messageId = responseData.messageId || `msg_${Date.now()}`;

        await this.logEmailEvent(
          'success',
          emailType,
          template.to,
          `Email sent successfully in ${duration}ms`,
          messageId
        );

        console.log(`✅ Email sent successfully to ${template.to} (${duration}ms)`);
        return { success: true, messageId };
      } else {
        const error = responseData.error || `HTTP ${response.status}: ${response.statusText}`;
        await this.logEmailEvent('error', emailType, template.to, `Email API error: ${error}`);
        console.error('❌ Email API error:', error);
        return { success: false, error };
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      await this.logEmailEvent('error', emailType, template.to, `Email sending failed: ${errorMessage}`);
      console.error(`❌ Failed to send email to ${template.to} after ${duration}ms:`, error);

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  // Log email events for monitoring and debugging
  private async logEmailEvent(
    level: 'info' | 'success' | 'error',
    emailType: EmailType,
    recipient: string,
    message: string,
    messageId?: string
  ): Promise<void> {
    try {
      await loggingService.logSystemEvent(
        level === 'error' ? 'error' : 'info',
        'email_service',
        message,
        {
          emailType,
          recipient,
          messageId,
          timestamp: new Date().toISOString()
        }
      );
    } catch (logError) {
      console.error('Failed to log email event:', logError);
    }
  }

  // Send welcome email for new Discord OAuth users
  async sendWelcomeEmail(user: AuthUser): Promise<EmailSendResult> {
    try {
      const emailTemplate = this.generateWelcomeEmail(user);
      const result = await this.sendEmail(emailTemplate, 'welcome');

      if (result.success) {
        console.log(`✅ Welcome email sent to ${user.email} for user ${user.name}`);
      } else {
        console.error(`❌ Failed to send welcome email to ${user.email}:`, result.error);
      }

      return result;
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Send purchase confirmation email
  async sendPurchaseConfirmation(
    userEmail: string,
    userName: string,
    order: Order
  ): Promise<EmailSendResult> {
    try {
      const emailTemplate = this.generatePurchaseConfirmationEmail(
        userEmail,
        userName,
        order
      );

      const result = await this.sendEmail(emailTemplate, 'order_confirmation');

      if (result.success) {
        console.log(`✅ Purchase confirmation email sent to ${userEmail} for order ${order.orderNumber}`);
      } else {
        console.error(`❌ Failed to send purchase confirmation email to ${userEmail} for order ${order.orderNumber}:`, result.error);
      }

      return result;
    } catch (error) {
      console.error('Error sending purchase confirmation:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Send credit transaction notification email
  async sendCreditTransactionNotification(
    userEmail: string,
    userName: string,
    transactionType: 'earned' | 'spent' | 'bonus',
    amount: number,
    description: string,
    newBalance: number
  ): Promise<EmailSendResult> {
    try {
      const emailTemplate = this.generateCreditTransactionEmail(
        userEmail,
        userName,
        transactionType,
        amount,
        description,
        newBalance
      );

      const result = await this.sendEmail(emailTemplate, 'credit_transaction');

      if (result.success) {
        console.log(`✅ Credit transaction email sent to ${userEmail} for ${transactionType} of $${amount}`);
      } else {
        console.error(`❌ Failed to send credit transaction email to ${userEmail}:`, result.error);
      }

      return result;
    } catch (error) {
      console.error('Error sending credit transaction notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Send admin alert email
  async sendAdminAlert(
    adminEmail: string,
    alertType: 'error' | 'warning' | 'info' | 'critical',
    title: string,
    message: string,
    details?: Record<string, any>
  ): Promise<EmailSendResult> {
    try {
      const emailTemplate = this.generateAdminAlertEmail(
        adminEmail,
        alertType,
        title,
        message,
        details
      );

      const result = await this.sendEmail(emailTemplate, 'admin_alert');

      if (result.success) {
        console.log(`✅ Admin alert email sent to ${adminEmail} for ${alertType}: ${title}`);
      } else {
        console.error(`❌ Failed to send admin alert email to ${adminEmail}:`, result.error);
      }

      return result;
    } catch (error) {
      console.error('Error sending admin alert:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Batch send emails with rate limiting
  async sendBatchEmails(
    templates: Array<{ template: EmailTemplate; type: EmailType }>,
    batchSize: number = 5,
    delayMs: number = 1000
  ): Promise<EmailSendResult[]> {
    const results: EmailSendResult[] = [];

    for (let i = 0; i < templates.length; i += batchSize) {
      const batch = templates.slice(i, i + batchSize);

      const batchPromises = batch.map(({ template, type }) =>
        this.sendEmail(template, type)
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to avoid rate limiting
      if (i + batchSize < templates.length) {
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }
    }

    return results;
  }

  // Get email service status and configuration
  getServiceStatus(): {
    configured: boolean;
    smtpHost?: string;
    fromEmail?: string;
    testMode: boolean;
  } {
    return {
      configured: !!this.smtpConfig,
      smtpHost: this.smtpConfig?.host,
      fromEmail: this.smtpConfig?.from.email,
      testMode: process.env.NODE_ENV === 'development' || !this.smtpConfig
    };
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();
