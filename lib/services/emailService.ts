// Email Service for Purchase Confirmations
// This is a basic structure that can be integrated with Appwrite Functions or Next.js API routes

import { Order, OrderItem } from '@/types';

export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
}

export class EmailService {
  private static instance: EmailService;
  
  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  // Generate purchase confirmation email HTML
  generatePurchaseConfirmationEmail(
    userEmail: string, 
    userName: string, 
    order: Order
  ): EmailTemplate {
    const totalImageStock = order.items.reduce((total, item) => {
      return total + (item.imageStock * item.quantity);
    }, 0);

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Purchase Confirmation - AveImgCloud</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
            color: white;
            padding: 2rem;
            text-align: center;
          }
          .header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
          }
          .content {
            padding: 2rem;
          }
          .order-details {
            background: #f1f5f9;
            border-radius: 6px;
            padding: 1.5rem;
            margin: 1.5rem 0;
          }
          .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e2e8f0;
          }
          .order-item:last-child {
            border-bottom: none;
          }
          .item-name {
            font-weight: 500;
            color: #1e293b;
          }
          .item-details {
            color: #64748b;
            font-size: 0.875rem;
          }
          .item-value {
            font-weight: 600;
            color: #0891b2;
          }
          .total-section {
            background: #0891b2;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 6px;
            margin: 1.5rem 0;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .footer {
            background: #1e293b;
            color: #94a3b8;
            padding: 1.5rem 2rem;
            text-align: center;
            font-size: 0.875rem;
          }
          .footer a {
            color: #0891b2;
            text-decoration: none;
          }
          .highlight {
            background: #dbeafe;
            color: #1e40af;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin: 1rem 0;
            border-left: 4px solid #3b82f6;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Purchase Confirmed!</h1>
            <p>Thank you for your purchase, ${userName}!</p>
          </div>
          
          <div class="content">
            <p>Your order has been processed successfully. Here are the details:</p>
            
            <div class="order-details">
              <h3 style="margin-top: 0; color: #1e293b;">Order #${order.orderNumber}</h3>
              <p style="color: #64748b; margin-bottom: 1rem;">
                Purchase Date: ${new Date(order.purchaseDate).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
              
              ${order.items.map(item => `
                <div class="order-item">
                  <div>
                    <div class="item-name">${item.planName}</div>
                    <div class="item-details">
                      ${item.imageStock} images × ${item.quantity} 
                      ${item.quantity > 1 ? 'plans' : 'plan'}
                    </div>
                  </div>
                  <div class="item-value">
                    ${item.creditCost * item.quantity} credits
                  </div>
                </div>
              `).join('')}
            </div>
            
            <div class="total-section">
              <div class="total-row">
                <span>Total Credits Used:</span>
                <span style="font-size: 1.25rem; font-weight: 700;">
                  ${order.totalCredits} credits
                </span>
              </div>
              ${order.discountAmount > 0 ? `
                <div class="total-row" style="margin-top: 0.5rem; font-size: 0.875rem;">
                  <span>Discount Applied:</span>
                  <span>-${order.discountAmount} credits</span>
                </div>
              ` : ''}
            </div>
            
            <div class="highlight">
              <strong>🖼️ Image Limit Increased!</strong><br>
              Your account now has ${totalImageStock} additional image uploads available.
            </div>
            
            <p>
              You can start uploading images immediately. Your new image allowance has been 
              automatically added to your account.
            </p>
            
            <p>
              If you have any questions or need assistance, please don't hesitate to 
              <a href="mailto:<EMAIL>" style="color: #0891b2;">contact our support team</a>.
            </p>
            
            <p>Thank you for choosing AveImgCloud!</p>
          </div>
          
          <div class="footer">
            <p>
              This email was sent to ${userEmail}<br>
              <a href="https://aveimgcloud.com">AveImgCloud</a> | 
              <a href="https://aveimgcloud.com/terms">Terms of Service</a> | 
              <a href="https://aveimgcloud.com/privacy">Privacy Policy</a>
            </p>
          </div>
        </div>
      </body>
      </html>
    `;

    return {
      to: userEmail,
      subject: `Purchase Confirmed - Order #${order.orderNumber} | AveImgCloud`,
      html: html.trim()
    };
  }

  // Send email via API
  async sendEmail(template: EmailTemplate): Promise<boolean> {
    try {
      // For development/testing, we'll log the email
      if (process.env.NODE_ENV === 'development') {
        console.log('Sending email:', {
          to: template.to,
          subject: template.subject,
          htmlLength: template.html.length
        });
      }

      // Send via our API endpoint
      const response = await fetch('/api/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(template)
      });

      if (response.ok) {
        console.log(`Email sent successfully to ${template.to}`);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('Email API error:', errorData);
        return false;
      }
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  // Send purchase confirmation email
  async sendPurchaseConfirmation(
    userEmail: string,
    userName: string,
    order: Order
  ): Promise<boolean> {
    try {
      const emailTemplate = this.generatePurchaseConfirmationEmail(
        userEmail, 
        userName, 
        order
      );
      
      const success = await this.sendEmail(emailTemplate);
      
      if (success) {
        console.log(`Purchase confirmation email sent to ${userEmail} for order ${order.orderNumber}`);
      } else {
        console.error(`Failed to send purchase confirmation email to ${userEmail} for order ${order.orderNumber}`);
      }
      
      return success;
    } catch (error) {
      console.error('Error sending purchase confirmation:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = EmailService.getInstance();
