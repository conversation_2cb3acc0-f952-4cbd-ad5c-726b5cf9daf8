'use client';

import { databases, ID } from '@/lib/appwrite/config';
import { appwriteConfig } from '@/lib/appwrite/config';
import { AdminLog, CreditTransaction } from '@/types';
import { Query } from 'appwrite';

export interface SystemLog {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  type: 'auth' | 'upload' | 'system' | 'error' | 'warning' | 'info';
  userId?: string;
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface LogFilters {
  type?: string;
  action?: string;
  userId?: string;
  adminUserId?: string;
  targetType?: string;
  success?: boolean | 'all';
  severity?: string;
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
}

export class LoggingService {
  // ========== ADMIN LOGS ==========

  // Enhanced admin log method with better error handling
  async logAdminAction(log: Omit<AdminLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        {
          ...log,
          timestamp: new Date().toISOString(),
          ipAddress: log.ipAddress || await this.getClientIP(),
          userAgent: log.userAgent || (typeof window !== 'undefined' ? navigator.userAgent : 'Server')
        }
      );
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Fallback to console logging if database fails
      console.log('ADMIN_ACTION:', log);
    }
  }

  // Get admin logs with advanced filtering
  async getAdminLogs(
    limit: number = 50, 
    offset: number = 0, 
    filters?: LogFilters
  ): Promise<AdminLog[]> {
    try {
      const queries = [
        Query.orderDesc('$createdAt'),
        Query.limit(limit),
        Query.offset(offset)
      ];

      // Apply filters
      if (filters?.action) {
        queries.push(Query.equal('action', filters.action));
      }
      if (filters?.adminUserId) {
        queries.push(Query.equal('adminUserId', filters.adminUserId));
      }
      if (filters?.targetType) {
        queries.push(Query.equal('targetType', filters.targetType));
      }
      if (filters?.success !== undefined && filters.success !== 'all') {
        queries.push(Query.equal('success', filters.success));
      }
      if (filters?.dateFrom) {
        queries.push(Query.greaterThanEqual('$createdAt', filters.dateFrom));
      }
      if (filters?.dateTo) {
        queries.push(Query.lessThanEqual('$createdAt', filters.dateTo));
      }

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        queries
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      throw new Error('Failed to fetch admin logs');
    }
  }

  // ========== SYSTEM LOGS ==========

  // Log authentication events
  async logAuthEvent(
    action: 'login' | 'logout' | 'oauth_start' | 'oauth_success' | 'oauth_failure' | 'session_expired',
    userId?: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'auth',
      action,
      userId,
      details: details || `User ${action}`,
      metadata,
      severity: action.includes('failure') ? 'medium' : 'low'
    });
  }

  // Log image upload/deletion events
  async logImageEvent(
    action: 'upload' | 'delete' | 'view' | 'share',
    userId: string,
    imageId?: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'upload',
      action: `image_${action}`,
      userId,
      details: details || `Image ${action}`,
      metadata: { imageId, ...metadata },
      severity: 'low'
    });
  }

  // Log system errors and warnings
  async logSystemError(
    error: Error | string,
    context?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    await this.logSystemEvent({
      type: 'error',
      action: 'system_error',
      userId,
      details: `${context ? `[${context}] ` : ''}${errorMessage}`,
      metadata: { stack: errorStack, ...metadata },
      severity: 'high'
    });
  }

  // Log system warnings
  async logSystemWarning(
    message: string,
    context?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'warning',
      action: 'system_warning',
      userId,
      details: `${context ? `[${context}] ` : ''}${message}`,
      metadata,
      severity: 'medium'
    });
  }

  // Log general system events
  async logSystemEvent(event: Omit<SystemLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      // For now, we'll use the admin logs collection for system logs too
      // In a production system, you might want a separate collection
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        {
          adminUserId: event.userId || 'system',
          action: event.action,
          targetType: 'system',
          targetId: event.metadata?.targetId,
          details: event.details,
          ipAddress: event.ipAddress || await this.getClientIP(),
          userAgent: event.userAgent || (typeof window !== 'undefined' ? navigator.userAgent : 'Server'),
          success: event.severity !== 'high' && event.severity !== 'critical',
          metadata: JSON.stringify(event.metadata || {})
        }
      );
    } catch (error) {
      console.error('Error logging system event:', error);
      // Fallback to console logging
      console.log('SYSTEM_EVENT:', event);
    }
  }

  // ========== CREDIT TRANSACTION LOGS ==========

  // Enhanced credit transaction logging
  async logCreditTransaction(transaction: Omit<CreditTransaction, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        ID.unique(),
        {
          ...transaction,
          ipAddress: transaction.ipAddress || await this.getClientIP()
        }
      );

      // Also log as admin action for comprehensive tracking
      await this.logAdminAction({
        adminUserId: 'system',
        action: `credit_${transaction.type}`,
        targetType: 'user',
        targetId: transaction.userId,
        details: `${transaction.type}: ${transaction.amount} credits - ${transaction.description}`,
        success: true
      });
    } catch (error) {
      console.error('Error logging credit transaction:', error);
    }
  }

  // ========== UTILITY METHODS ==========

  // Get client IP address (simplified for demo)
  private async getClientIP(): Promise<string> {
    try {
      if (typeof window !== 'undefined') {
        // In a real app, you might use a service to get the real IP
        return 'client-ip';
      }
      return 'server-ip';
    } catch {
      return 'unknown';
    }
  }

  // Search logs across all types
  async searchLogs(
    searchTerm: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<AdminLog[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        [
          Query.search('details', searchTerm),
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error searching logs:', error);
      return [];
    }
  }

  // Get log statistics
  async getLogStats(dateFrom?: string, dateTo?: string): Promise<{
    totalLogs: number;
    successfulActions: number;
    failedActions: number;
    errorCount: number;
    warningCount: number;
  }> {
    try {
      const queries = [Query.limit(1)];
      
      if (dateFrom) {
        queries.push(Query.greaterThanEqual('$createdAt', dateFrom));
      }
      if (dateTo) {
        queries.push(Query.lessThanEqual('$createdAt', dateTo));
      }

      const [total, successful, failed] = await Promise.all([
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, queries),
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, [...queries, Query.equal('success', true)]),
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, [...queries, Query.equal('success', false)])
      ]);

      return {
        totalLogs: total.total,
        successfulActions: successful.total,
        failedActions: failed.total,
        errorCount: 0, // Would need separate tracking
        warningCount: 0 // Would need separate tracking
      };
    } catch (error) {
      console.error('Error getting log stats:', error);
      return {
        totalLogs: 0,
        successfulActions: 0,
        failedActions: 0,
        errorCount: 0,
        warningCount: 0
      };
    }
  }
}

// Export singleton instance
export const loggingService = new LoggingService();
