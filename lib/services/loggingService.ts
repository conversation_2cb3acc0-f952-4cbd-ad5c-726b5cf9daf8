import { databases, ID } from '@/lib/appwrite/config';
import { appwriteConfig } from '@/lib/appwrite/config';
import { AdminLog, CreditTransaction } from '@/types';
import { Query } from 'appwrite';

export interface SystemLog {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  type: 'auth' | 'upload' | 'system' | 'error' | 'warning' | 'info';
  userId?: string;
  action: string;
  details: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface LogFilters {
  type?: string;
  action?: string;
  userId?: string;
  adminUserId?: string;
  targetType?: string;
  success?: boolean | 'all';
  severity?: string;
  dateFrom?: string;
  dateTo?: string;
  searchTerm?: string;
}

// Comprehensive action types for detailed logging
export type UserActionType =
  | 'user_login' | 'user_logout' | 'user_register' | 'user_profile_update'
  | 'image_upload' | 'image_delete' | 'image_view' | 'image_share'
  | 'order_create' | 'order_complete' | 'order_cancel' | 'payment_process'
  | 'credit_add' | 'credit_deduct' | 'credit_purchase' | 'credit_bonus'
  | 'settings_update' | 'password_change' | 'email_change'
  | 'admin_action' | 'system_event' | 'security_alert'
  | 'email_sent' | 'email_failed' | 'email_bounce'
  | 'image_limit_warning' | 'storage_limit_warning';

// Enhanced log entry with comprehensive metadata
export interface EnhancedLogEntry {
  action: UserActionType;
  userId?: string;
  details: string;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    beforeValue?: any;
    afterValue?: any;
    targetId?: string;
    targetType?: string;
    sessionId?: string;
    requestId?: string;
    duration?: number;
    success?: boolean;
    errorCode?: string;
    errorMessage?: string;
    [key: string]: any;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class LoggingService {
  // ========== ADMIN LOGS ==========

  // Enhanced admin log method with better error handling
  async logAdminAction(log: Omit<AdminLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        {
          ...log,
          timestamp: new Date().toISOString(),
          ipAddress: log.ipAddress || await this.getClientIP(),
          userAgent: log.userAgent || (typeof window !== 'undefined' ? navigator.userAgent : 'Server')
        }
      );
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Fallback to console logging if database fails
      console.log('ADMIN_ACTION:', log);
    }
  }

  // Get admin logs with advanced filtering
  async getAdminLogs(
    limit: number = 50, 
    offset: number = 0, 
    filters?: LogFilters
  ): Promise<AdminLog[]> {
    try {
      const queries = [
        Query.orderDesc('$createdAt'),
        Query.limit(limit),
        Query.offset(offset)
      ];

      // Apply filters
      if (filters?.action) {
        queries.push(Query.equal('action', filters.action));
      }
      if (filters?.adminUserId) {
        queries.push(Query.equal('adminUserId', filters.adminUserId));
      }
      if (filters?.targetType) {
        queries.push(Query.equal('targetType', filters.targetType));
      }
      if (filters?.success !== undefined && filters.success !== 'all') {
        queries.push(Query.equal('success', filters.success));
      }
      if (filters?.dateFrom) {
        queries.push(Query.greaterThanEqual('$createdAt', filters.dateFrom));
      }
      if (filters?.dateTo) {
        queries.push(Query.lessThanEqual('$createdAt', filters.dateTo));
      }

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        queries
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      throw new Error('Failed to fetch admin logs');
    }
  }

  // ========== SYSTEM LOGS ==========

  // Log authentication events
  async logAuthEvent(
    action: 'login' | 'logout' | 'oauth_start' | 'oauth_success' | 'oauth_failure' | 'session_expired',
    userId?: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'auth',
      action,
      userId,
      details: details || `User ${action}`,
      metadata,
      severity: action.includes('failure') ? 'medium' : 'low'
    });
  }

  // Log image upload/deletion events
  async logImageEvent(
    action: 'upload' | 'delete' | 'view' | 'share',
    userId: string,
    imageId?: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'upload',
      action: `image_${action}`,
      userId,
      details: details || `Image ${action}`,
      metadata: { imageId, ...metadata },
      severity: 'low'
    });
  }

  // Log system errors and warnings
  async logSystemError(
    error: Error | string,
    context?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;

    await this.logSystemEvent({
      type: 'error',
      action: 'system_error',
      userId,
      details: `${context ? `[${context}] ` : ''}${errorMessage}`,
      metadata: { stack: errorStack, ...metadata },
      severity: 'high'
    });
  }

  // Log system warnings
  async logSystemWarning(
    message: string,
    context?: string,
    userId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logSystemEvent({
      type: 'warning',
      action: 'system_warning',
      userId,
      details: `${context ? `[${context}] ` : ''}${message}`,
      metadata,
      severity: 'medium'
    });
  }

  // Log general system events
  async logSystemEvent(event: Omit<SystemLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      // For now, we'll use the admin logs collection for system logs too
      // In a production system, you might want a separate collection
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        {
          adminUserId: event.userId || 'system',
          action: event.action,
          targetType: 'system',
          targetId: event.metadata?.targetId,
          details: event.details,
          ipAddress: event.ipAddress || await this.getClientIP(),
          userAgent: event.userAgent || (typeof window !== 'undefined' ? navigator.userAgent : 'Server'),
          success: event.severity !== 'high' && event.severity !== 'critical',
          metadata: JSON.stringify(event.metadata || {})
        }
      );
    } catch (error) {
      console.error('Error logging system event:', error);
      // Fallback to console logging
      console.log('SYSTEM_EVENT:', event);
    }
  }

  // ========== CREDIT TRANSACTION LOGS ==========

  // Enhanced credit transaction logging
  async logCreditTransaction(transaction: Omit<CreditTransaction, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        ID.unique(),
        {
          ...transaction,
          ipAddress: transaction.ipAddress || await this.getClientIP()
        }
      );

      // Also log as admin action for comprehensive tracking
      await this.logAdminAction({
        adminUserId: 'system',
        action: `credit_${transaction.type}`,
        targetType: 'user',
        targetId: transaction.userId,
        details: `${transaction.type}: ${transaction.amount} credits - ${transaction.description}`,
        success: true
      });
    } catch (error) {
      console.error('Error logging credit transaction:', error);
    }
  }

  // ========== UTILITY METHODS ==========

  // Get client IP address (simplified for demo)
  private async getClientIP(): Promise<string> {
    try {
      if (typeof window !== 'undefined') {
        // In a real app, you might use a service to get the real IP
        return 'client-ip';
      }
      return 'server-ip';
    } catch {
      return 'unknown';
    }
  }

  // Search logs across all types
  async searchLogs(
    searchTerm: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<AdminLog[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        [
          Query.search('details', searchTerm),
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error searching logs:', error);
      return [];
    }
  }

  // Get log statistics
  async getLogStats(dateFrom?: string, dateTo?: string): Promise<{
    totalLogs: number;
    successfulActions: number;
    failedActions: number;
    errorCount: number;
    warningCount: number;
  }> {
    try {
      const queries = [Query.limit(1)];
      
      if (dateFrom) {
        queries.push(Query.greaterThanEqual('$createdAt', dateFrom));
      }
      if (dateTo) {
        queries.push(Query.lessThanEqual('$createdAt', dateTo));
      }

      const [total, successful, failed] = await Promise.all([
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, queries),
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, [...queries, Query.equal('success', true)]),
        databases.listDocuments(appwriteConfig.databaseId, appwriteConfig.adminLogsCollectionId, [...queries, Query.equal('success', false)])
      ]);

      return {
        totalLogs: total.total,
        successfulActions: successful.total,
        failedActions: failed.total,
        errorCount: 0, // Would need separate tracking
        warningCount: 0 // Would need separate tracking
      };
    } catch (error) {
      console.error('Error getting log stats:', error);
      return {
        totalLogs: 0,
        successfulActions: 0,
        failedActions: 0,
        errorCount: 0,
        warningCount: 0
      };
    }
  }

  // ========== COMPREHENSIVE USER ACTION LOGGING ==========

  // Enhanced user action logging with comprehensive metadata
  async logUserAction(entry: EnhancedLogEntry & {
    type?: 'auth' | 'upload' | 'system' | 'error' | 'warning' | 'info';
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    try {
      const logType = entry.type || this.getLogTypeFromAction(entry.action);

      await this.logSystemEvent({
        type: logType,
        action: entry.action,
        details: entry.details,
        metadata: {
          ...entry.metadata,
          timestamp: new Date().toISOString(),
          actionType: entry.action,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent
        },
        severity: entry.severity,
        userId: entry.userId
      });
    } catch (error) {
      console.error('Failed to log user action:', error);
    }
  }

  // Determine log type from action
  private getLogTypeFromAction(action: UserActionType): 'auth' | 'upload' | 'system' | 'error' | 'warning' | 'info' {
    if (action.includes('login') || action.includes('logout') || action.includes('register')) {
      return 'auth';
    }
    if (action.includes('upload') || action.includes('image')) {
      return 'upload';
    }
    if (action.includes('error') || action.includes('failed')) {
      return 'error';
    }
    if (action.includes('warning') || action.includes('limit')) {
      return 'warning';
    }
    return 'info';
  }

  // Log user profile updates with before/after values
  async logProfileUpdate(
    userId: string,
    field: string,
    beforeValue: any,
    afterValue: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'user_profile_update',
      userId,
      details: `User profile updated: ${field}`,
      metadata: {
        field,
        beforeValue,
        afterValue,
        ipAddress,
        userAgent
      },
      severity: 'low'
    });
  }

  // Log image upload with comprehensive metadata
  async logImageUpload(
    userId: string,
    imageId: string,
    filename: string,
    fileSize: number,
    mimeType: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'image_upload',
      userId,
      details: `Image uploaded: ${filename}`,
      metadata: {
        imageId,
        filename,
        fileSize,
        mimeType,
        ipAddress,
        userAgent
      },
      severity: 'low'
    });
  }

  // Log order creation with detailed information
  async logOrderCreation(
    userId: string,
    orderId: string,
    orderDetails: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'order_create',
      userId,
      details: `Order created: ${orderId}`,
      metadata: {
        orderId,
        orderDetails,
        ipAddress,
        userAgent
      },
      severity: 'medium'
    });
  }

  // Log payment processing
  async logPaymentProcess(
    userId: string,
    orderId: string,
    amount: number,
    status: 'success' | 'failed' | 'pending',
    paymentMethod: string,
    transactionId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'payment_process',
      userId,
      details: `Payment ${status} for order ${orderId}: $${amount}`,
      metadata: {
        orderId,
        amount,
        status,
        paymentMethod,
        transactionId,
        ipAddress,
        userAgent,
        success: status === 'success'
      },
      severity: status === 'failed' ? 'high' : 'medium'
    });
  }

  // Log settings changes with before/after values
  async logSettingsChange(
    userId: string,
    setting: string,
    beforeValue: any,
    afterValue: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'settings_update',
      userId,
      details: `Settings updated: ${setting}`,
      metadata: {
        setting,
        beforeValue,
        afterValue,
        ipAddress,
        userAgent
      },
      severity: 'low'
    });
  }

  // Log email events with delivery status
  async logEmailEvent(
    type: 'sent' | 'failed' | 'bounce',
    recipient: string,
    subject: string,
    emailType: string,
    userId?: string,
    errorMessage?: string
  ): Promise<void> {
    await this.logUserAction({
      action: type === 'sent' ? 'email_sent' : type === 'failed' ? 'email_failed' : 'email_bounce',
      userId,
      details: `Email ${type}: ${subject} to ${recipient}`,
      metadata: {
        recipient,
        subject,
        emailType,
        errorMessage,
        success: type === 'sent'
      },
      severity: type === 'sent' ? 'low' : 'medium'
    });
  }

  // Log security alerts
  async logSecurityAlert(
    userId: string,
    alertType: string,
    details: string,
    ipAddress?: string,
    userAgent?: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'high'
  ): Promise<void> {
    await this.logUserAction({
      action: 'security_alert',
      userId,
      details: `Security Alert: ${alertType} - ${details}`,
      metadata: {
        alertType,
        ipAddress,
        userAgent
      },
      severity
    });
  }

  // Log admin actions with comprehensive audit trail
  async logAdminActionDetailed(
    adminUserId: string,
    action: string,
    targetUserId?: string,
    targetType?: string,
    beforeValue?: any,
    afterValue?: any,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logUserAction({
      action: 'admin_action',
      userId: adminUserId,
      details: `Admin action: ${action}${targetUserId ? ` on user ${targetUserId}` : ''}`,
      metadata: {
        adminAction: action,
        targetUserId,
        targetType,
        beforeValue,
        afterValue,
        ipAddress,
        userAgent
      },
      severity: 'high'
    });
  }
}

// Export singleton instance
export const loggingService = new LoggingService();
