import { databases, ID } from '@/lib/appwrite/config';
import { appwriteConfig } from '@/lib/appwrite/config';
import { AdminLog } from '@/types';
import { Query } from 'appwrite';

// Simplified logging service - only basic error logging for debugging
export class LoggingService {
  // Basic error logging for debugging purposes only
  async logError(message: string, details?: string): Promise<void> {
    try {
      console.error('Error logged:', message, details);
      // Could store in database if needed for debugging
    } catch (error) {
      console.error('Failed to log error:', error);
    }
  }

  // Basic warning logging
  async logWarning(message: string, details?: string): Promise<void> {
    try {
      console.warn('Warning logged:', message, details);
      // Could store in database if needed for debugging
    } catch (error) {
      console.error('Failed to log warning:', error);
    }
  }

  // Keep basic admin logs functionality for existing admin pages
  async getAdminLogs(
    limit: number = 50,
    offset: number = 0
  ): Promise<AdminLog[]> {
    try {
      const queries = [
        Query.orderDesc('$createdAt'),
        Query.limit(limit),
        Query.offset(offset)
      ];

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        queries
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      return [];
    }
  }

  // Basic admin action logging (simplified)
  async logAdminAction(log: Omit<AdminLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        {
          ...log,
          timestamp: new Date().toISOString()
        }
      );
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Fallback to console logging if database fails
      console.log('ADMIN_ACTION:', log);
    }
  }

  // Get basic log statistics
  async getLogStats(): Promise<{
    totalLogs: number;
    successfulActions: number;
    failedActions: number;
    errorCount: number;
    warningCount: number;
  }> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        [Query.limit(1000)] // Get recent logs for stats
      );

      const logs = response.documents as unknown as AdminLog[];

      return {
        totalLogs: logs.length,
        successfulActions: logs.filter(log => log.success === true).length,
        failedActions: logs.filter(log => log.success === false).length,
        errorCount: logs.filter(log => log.action?.includes('error')).length,
        warningCount: logs.filter(log => log.action?.includes('warning')).length
      };
    } catch (error) {
      console.error('Error getting log stats:', error);
      return {
        totalLogs: 0,
        successfulActions: 0,
        failedActions: 0,
        errorCount: 0,
        warningCount: 0
      };
    }
  }

  // Search logs (simplified)
  async searchLogs(searchTerm: string, limit: number = 50, offset: number = 0): Promise<AdminLog[]> {
    try {
      const queries = [
        Query.search('details', searchTerm),
        Query.orderDesc('$createdAt'),
        Query.limit(limit),
        Query.offset(offset)
      ];

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        queries
      );

      return response.documents as AdminLog[];
    } catch (error) {
      console.error('Error searching logs:', error);
      return [];
    }
  }
}

// Export singleton instance
export const loggingService = new LoggingService();