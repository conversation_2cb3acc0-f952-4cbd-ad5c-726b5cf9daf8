import { storage, databases, appwriteConfig, ID } from '@/lib/appwrite/config';
import { Query } from 'appwrite';
import { UploadedImage } from '@/types';
import { v4 as uuidv4 } from 'uuid';

export class UploadService {
  // Supported file types
  private readonly SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff'
  ];

  private readonly SUPPORTED_VIDEO_TYPES = [
    'video/mp4',
    'video/mov',
    'video/avi',
    'video/wmv',
    'video/flv',
    'video/webm',
    'video/mkv'
  ];

  private readonly MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB

  // Validate file before upload
  validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: 'File size must be less than 100MB'
      };
    }

    // Check file type
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.type);
    const isVideo = this.SUPPORTED_VIDEO_TYPES.includes(file.type);

    if (!isImage && !isVideo) {
      return {
        isValid: false,
        error: 'Unsupported file type. Please upload an image (JPEG, PNG, GIF, WebP) or video (MP4, MOV, AVI, etc.)'
      };
    }

    return { isValid: true };
  }

  // Check and send image limit warnings via API (server-side only)
  private async checkImageLimitWarnings(userId: string): Promise<void> {
    try {
      // Only run on server side to avoid Node.js module issues
      if (typeof window !== 'undefined') {
        console.log('Skipping image limit check on client side');
        return;
      }

      // Use API route to check limits and send warnings
      // This ensures server-side processing and proper logging
      const response = await fetch('/api/upload/check-limits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        if (result.warningSent) {
          console.log(`Image limit warning sent: ${result.warningType} usage for user ${userId}`);
        }
      } else {
        console.warn('Failed to check image limits via API:', response.statusText);
      }
    } catch (error) {
      console.warn('Failed to check image limit warnings:', error);
      // Don't throw error to avoid disrupting upload process
    }
  }

  // Upload file to Appwrite storage
  async uploadFile(
    file: File,
    userId: string,
    onProgress?: (progress: number) => void
  ): Promise<UploadedImage> {
    try {
      // Validate file first
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Check if collection ID is configured
      if (!appwriteConfig.uploadedImagesCollectionId) {
        throw new Error('Upload collection not configured. Please contact administrator.');
      }

      // Generate unique ID for the file
      const uniqueId = this.generateUniqueId();
      const fileId = ID.unique();

      // Upload to Appwrite storage
      const uploadedFile = await storage.createFile(
        appwriteConfig.storageId,
        fileId,
        file,
        undefined, // permissions
        onProgress ? (progress) => {
          const percentage = Math.round((progress.chunksUploaded / progress.chunksTotal) * 100);
          onProgress(percentage);
        } : undefined
      );

      // Create database record
      const imageRecord = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.uploadedImagesCollectionId,
        ID.unique(),
        {
          userId,
          filename: file.name,
          fileId: uploadedFile.$id,
          mimeType: file.type,
          fileSize: file.size,
          uniqueId,
          uploadDate: new Date().toISOString(),
          isPublic: true
        }
      );

      const uploadedImage = {
        $id: imageRecord.$id,
        $createdAt: imageRecord.$createdAt,
        $updatedAt: imageRecord.$updatedAt,
        userId: imageRecord.userId,
        filename: imageRecord.filename,
        fileId: imageRecord.fileId,
        mimeType: imageRecord.mimeType,
        fileSize: imageRecord.fileSize,
        uniqueId: imageRecord.uniqueId,
        uploadDate: imageRecord.uploadDate,
        isPublic: imageRecord.isPublic
      };

      // Check for image limit warnings after successful upload
      // Run asynchronously to not delay the upload response
      this.checkImageLimitWarnings(userId).catch(error => {
        console.warn('Failed to check image limit warnings:', error);
      });

      return uploadedImage;
    } catch (error: any) {
      console.error('Upload error:', error);
      throw new Error(error.message || 'Failed to upload file');
    }
  }

  // Generate unique ID in format: xxxxx-xxxxx-xxxxx-xxxxx
  private generateUniqueId(): string {
    const uuid = uuidv4().replace(/-/g, '');
    return [
      uuid.substring(0, 5),
      uuid.substring(5, 10),
      uuid.substring(10, 15),
      uuid.substring(15, 20)
    ].join('-');
  }

  // Get file URL from Appwrite storage
  getFileUrl(fileId: string): string {
    return storage.getFileView(appwriteConfig.storageId, fileId);
  }

  // Get file download URL
  getFileDownloadUrl(fileId: string): string {
    return storage.getFileDownload(appwriteConfig.storageId, fileId);
  }

  // Get user's uploaded images
  async getUserImages(userId: string, limit: number = 50, offset: number = 0): Promise<UploadedImage[]> {
    try {
      // Check if collection ID is configured
      if (!appwriteConfig.uploadedImagesCollectionId) {
        console.warn('uploadedImagesCollectionId not configured');
        return [];
      }

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.uploadedImagesCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents.map(doc => ({
        $id: doc.$id,
        $createdAt: doc.$createdAt,
        $updatedAt: doc.$updatedAt,
        userId: doc.userId,
        filename: doc.filename,
        fileId: doc.fileId,
        mimeType: doc.mimeType,
        fileSize: doc.fileSize,
        uniqueId: doc.uniqueId,
        uploadDate: doc.uploadDate,
        isPublic: doc.isPublic
      }));
    } catch (error: any) {
      console.error('Error fetching user images:', error);
      // Return empty array instead of throwing error to prevent page crashes
      return [];
    }
  }

  // Get image by unique ID
  async getImageByUniqueId(uniqueId: string): Promise<UploadedImage | null> {
    try {
      // Check if collection ID is configured
      if (!appwriteConfig.uploadedImagesCollectionId) {
        console.warn('uploadedImagesCollectionId not configured');
        return null;
      }

      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.uploadedImagesCollectionId,
        [
          Query.equal('uniqueId', uniqueId),
          Query.limit(1)
        ]
      );

      if (response.documents.length === 0) {
        return null;
      }

      const doc = response.documents[0];
      return {
        $id: doc.$id,
        $createdAt: doc.$createdAt,
        $updatedAt: doc.$updatedAt,
        userId: doc.userId,
        filename: doc.filename,
        fileId: doc.fileId,
        mimeType: doc.mimeType,
        fileSize: doc.fileSize,
        uniqueId: doc.uniqueId,
        uploadDate: doc.uploadDate,
        isPublic: doc.isPublic
      };
    } catch (error: any) {
      console.error('Error fetching image by unique ID:', error);
      return null;
    }
  }

  // Format file size for display
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Check if file type is image
  isImageFile(mimeType: string): boolean {
    return this.SUPPORTED_IMAGE_TYPES.includes(mimeType);
  }

  // Check if file type is video
  isVideoFile(mimeType: string): boolean {
    return this.SUPPORTED_VIDEO_TYPES.includes(mimeType);
  }
}

// Export singleton instance
export const uploadService = new UploadService();
