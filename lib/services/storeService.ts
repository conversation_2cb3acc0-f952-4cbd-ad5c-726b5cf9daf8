'use client';

import { databases, ID } from '@/lib/appwrite/config';
import { appwriteConfig } from '@/lib/appwrite/config';
import { StorePlan, CartItem, Order, Coupon, CreditTransaction, AdminLog, CartSummary, CheckoutData, CheckoutSummary, OrderItem } from '@/types';
import { Query } from 'appwrite';
import { userService } from './userService';
import { account } from '../appwrite/config';

export class StoreService {
  // ========== STORE PLANS ==========

  // Get all active store plans
  async getActivePlans(): Promise<StorePlan[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.storePlansCollectionId,
        [
          Query.equal('isActive', true),
          Query.orderDesc('$createdAt')
        ]
      );

      return response.documents as unknown as StorePlan[];
    } catch (error) {
      // Only log errors on client-side to avoid build-time errors
      if (typeof window !== 'undefined') {
        console.error('Error fetching active plans:', error);
      }
      throw new Error('Failed to fetch store plans');
    }
  }

  // Get all plans (admin only)
  async getAllPlans(): Promise<StorePlan[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.storePlansCollectionId,
        [Query.orderDesc('$createdAt')]
      );

      return response.documents as unknown as StorePlan[];
    } catch (error) {
      // Only log errors on client-side to avoid build-time errors
      if (typeof window !== 'undefined') {
        console.error('Error fetching all plans:', error);
      }
      throw new Error('Failed to fetch store plans');
    }
  }

  // Create new store plan (admin only)
  async createPlan(planData: Omit<StorePlan, '$id' | '$createdAt' | '$updatedAt'>): Promise<StorePlan> {
    try {
      const response = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.storePlansCollectionId,
        ID.unique(),
        planData
      );

      return response as unknown as StorePlan;
    } catch (error) {
      console.error('Error creating plan:', error);
      throw new Error('Failed to create store plan');
    }
  }

  // Update store plan (admin only)
  async updatePlan(planId: string, updates: Partial<StorePlan>): Promise<StorePlan> {
    try {
      const response = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.storePlansCollectionId,
        planId,
        updates
      );

      return response as unknown as StorePlan;
    } catch (error) {
      console.error('Error updating plan:', error);
      throw new Error('Failed to update store plan');
    }
  }

  // Delete store plan (admin only)
  async deletePlan(planId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.storePlansCollectionId,
        planId
      );
    } catch (error) {
      console.error('Error deleting plan:', error);
      throw new Error('Failed to delete store plan');
    }
  }

  // ========== SHOPPING CART ==========

  // Get user's cart items
  async getCartItems(userId: string): Promise<CartItem[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.shoppingCartCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('addedAt')
        ]
      );

      // Populate plan data for each cart item
      const cartItems = response.documents as unknown as CartItem[];
      const itemsWithPlan = await Promise.all(
        cartItems.map(async (item) => {
          try {
            const plan = await databases.getDocument(
              appwriteConfig.databaseId,
              appwriteConfig.storePlansCollectionId,
              item.planId
            );
            return { ...item, plan: plan as unknown as StorePlan };
          } catch (error) {
            console.warn(`Plan ${item.planId} not found for cart item ${item.$id}`);
            return item;
          }
        })
      );

      return itemsWithPlan;
    } catch (error) {
      console.error('Error fetching cart items:', error);
      throw new Error('Failed to fetch cart items');
    }
  }

  // Add item to cart
  async addToCart(userId: string, planId: string): Promise<CartItem> {
    try {
      // Check if item already exists in cart
      const existingItems = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.shoppingCartCollectionId,
        [
          Query.equal('userId', userId),
          Query.equal('planId', planId)
        ]
      );

      if (existingItems.documents.length > 0) {
        throw new Error('Item already in cart');
      }

      const response = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.shoppingCartCollectionId,
        ID.unique(),
        {
          userId,
          planId,
          quantity: 1,
          addedAt: new Date().toISOString()
        }
      );

      return response as unknown as CartItem;
    } catch (error) {
      console.error('Error adding to cart:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to add item to cart');
    }
  }

  // Remove item from cart
  async removeFromCart(cartItemId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.shoppingCartCollectionId,
        cartItemId
      );
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw new Error('Failed to remove item from cart');
    }
  }

  // Clear user's cart
  async clearCart(userId: string): Promise<void> {
    try {
      const cartItems = await this.getCartItems(userId);
      const deletePromises = cartItems.map(item => 
        this.removeFromCart(item.$id)
      );
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw new Error('Failed to clear cart');
    }
  }

  // Get cart summary
  async getCartSummary(userId: string): Promise<CartSummary> {
    try {
      const items = await this.getCartItems(userId);
      
      const totalItems = items.length;
      const totalCredits = items.reduce((sum, item) => 
        sum + (item.plan?.creditCost || 0), 0
      );
      const totalImageStock = items.reduce((sum, item) => 
        sum + (item.plan?.imageStock || 0), 0
      );

      return {
        items,
        totalItems,
        totalCredits,
        totalImageStock,
        subtotal: totalCredits,
        discount: 0,
        total: totalCredits,
        couponCode: undefined,
        couponDetails: null
      };
    } catch (error) {
      console.error('Error getting cart summary:', error);
      throw new Error('Failed to get cart summary');
    }
  }

  // ========== COUPONS ==========

  // Validate coupon code
  async validateCoupon(code: string, cartTotal: number): Promise<Coupon> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        [
          Query.equal('code', code),
          Query.equal('isActive', true)
        ]
      );

      if (response.documents.length === 0) {
        throw new Error('Invalid coupon code');
      }

      const coupon = response.documents[0] as unknown as Coupon;

      // Check expiry date
      if (coupon.expiresAt && new Date(coupon.expiresAt) < new Date()) {
        throw new Error('Coupon has expired');
      }

      // Check usage limit
      if (coupon.usageLimit && coupon.usageLimit > 0 && coupon.usedCount >= coupon.usageLimit) {
        throw new Error('Coupon usage limit reached');
      }

      // Check minimum purchase requirement
      if (coupon.minimumAmount && coupon.minimumAmount > 0 && cartTotal < coupon.minimumAmount) {
        throw new Error(`Minimum purchase of ${coupon.minimumAmount} credits required`);
      }

      return coupon;
    } catch (error) {
      console.error('Error validating coupon:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to validate coupon');
    }
  }

  // Calculate discount amount
  calculateDiscount(coupon: Coupon, cartTotal: number): number {
    if (coupon.discountType === 'percentage') {
      return Math.floor((cartTotal * coupon.discountValue) / 100);
    } else {
      return Math.min(coupon.discountValue, cartTotal);
    }
  }

  // ========== CHECKOUT & ORDERS ==========

  // Process checkout
  async processCheckout(
    userId: string, 
    checkoutSummary: CheckoutSummary, 
    userCredits: number
  ): Promise<Order> {
    try {
      // Verify user has enough credits
      if (userCredits < checkoutSummary.total) {
        throw new Error('Insufficient credits');
      }

      // Generate unique order number
      const orderNumber = this.generateOrderNumber();

      // Prepare order items
      const orderItems: OrderItem[] = checkoutSummary.cartItems.map(item => ({
        planId: item.planId,
        planName: item.plan?.name || 'Unknown Plan',
        imageStock: item.plan?.imageStock || 0,
        creditCost: item.plan?.creditCost || 0,
        quantity: item.quantity
      }));

      // Create order
      const order = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        ID.unique(),
        {
          userId,
          orderNumber,
          items: JSON.stringify(orderItems),
          totalCredits: checkoutSummary.total,
          discountAmount: checkoutSummary.discount,
          couponCode: checkoutSummary.couponCode || null,
          status: 'completed',
          purchaseDate: new Date().toISOString(),
          emailSent: false
        }
      );

      // Update coupon usage if used
      if (checkoutSummary.couponCode) {
        await this.incrementCouponUsage(checkoutSummary.couponCode);
      }

      // Update user's image limit based on purchased plans
      const totalImageStockPurchased = orderItems.reduce((total, item) => {
        return total + (item.imageStock * item.quantity);
      }, 0);

      if (totalImageStockPurchased > 0) {
        try {
          const currentConfig = await userService.getUserConfiguration(userId);
          await userService.updateUserConfiguration(userId, {
            totalImagesLimit: currentConfig.totalImagesLimit + totalImageStockPurchased
          });
        } catch (error) {
          console.error('Failed to update user image limit:', error);
          // Don't fail the entire checkout if image limit update fails
        }
      }

      // Prepare order object for email
      const orderForEmail = { 
        ...(order as unknown as Order), 
        items: orderItems 
      };

      // Send purchase confirmation email
      try {
        const userAccount = await account.get();
        const userEmail = userAccount.email;
        const userName = userAccount.name || 'Customer';

        // Email service removed - order confirmation emails disabled
        const emailResult = { success: true, messageId: 'email-disabled' };
        console.log('Email service disabled - no confirmation email sent');

        // Update order to mark email as sent
        if (emailResult.success) {
          await databases.updateDocument(
            appwriteConfig.databaseId,
            appwriteConfig.ordersCollectionId,
            order.$id,
            { emailSent: true }
          );

          // Email service removed - credit transaction notifications disabled
          console.log('Email service disabled - no credit transaction notification sent');
        }
      } catch (emailError) {
        console.error('Failed to send purchase confirmation email:', emailError);
        // Don't fail the entire checkout if email sending fails
      }

      return orderForEmail;
    } catch (error) {
      console.error('Error processing checkout:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to process checkout');
    }
  }

  // Generate unique order number
  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp.slice(-8)}-${random}`;
  }

  // Increment coupon usage
  private async incrementCouponUsage(couponCode: string): Promise<void> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        [Query.equal('code', couponCode)]
      );

      if (response.documents.length > 0) {
        const coupon = response.documents[0] as unknown as Coupon;
        await databases.updateDocument(
          appwriteConfig.databaseId,
          appwriteConfig.couponsCollectionId,
          coupon.$id,
          { usedCount: coupon.usedCount + 1 }
        );
      }
    } catch (error) {
      console.error('Error incrementing coupon usage:', error);
      // Don't throw error here as order was successful
    }
  }

  // Get user's order history
  async getUserOrders(userId: string): Promise<Order[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('purchaseDate')
        ]
      );

      return response.documents.map(doc => ({
        ...(doc as unknown as Order),
        items: JSON.parse(doc.items || '[]')
      }));
    } catch (error) {
      console.error('Error fetching user orders:', error);
      throw new Error('Failed to fetch order history');
    }
  }

  // Get all orders (admin only)
  async getAllOrders(): Promise<Order[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [Query.orderDesc('purchaseDate')]
      );

      return response.documents.map(doc => ({
        ...(doc as unknown as Order),
        items: JSON.parse(doc.items || '[]')
      }));
    } catch (error) {
      console.error('Error fetching all orders:', error);
      throw new Error('Failed to fetch orders');
    }
  }

  // ========== COUPONS MANAGEMENT (Admin) ==========

  // Create coupon (admin only)
  async createCoupon(couponData: Omit<Coupon, '$id' | '$createdAt' | '$updatedAt' | 'usedCount'>): Promise<Coupon> {
    try {
      const response = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        ID.unique(),
        {
          ...couponData,
          usedCount: 0
        }
      );

      return response as unknown as Coupon;
    } catch (error) {
      console.error('Error creating coupon:', error);
      throw new Error('Failed to create coupon');
    }
  }

  // Get all coupons (admin only)
  async getAllCoupons(): Promise<Coupon[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        [Query.orderDesc('$createdAt')]
      );

      return response.documents as unknown as Coupon[];
    } catch (error) {
      console.error('Error fetching coupons:', error);
      throw new Error('Failed to fetch coupons');
    }
  }

  // Update coupon (admin only)
  async updateCoupon(couponId: string, updates: Partial<Coupon>): Promise<Coupon> {
    try {
      const response = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        couponId,
        updates
      );

      return response as unknown as Coupon;
    } catch (error) {
      console.error('Error updating coupon:', error);
      throw new Error('Failed to update coupon');
    }
  }

  // Delete coupon (admin only)
  async deleteCoupon(couponId: string): Promise<void> {
    try {
      await databases.deleteDocument(
        appwriteConfig.databaseId,
        appwriteConfig.couponsCollectionId,
        couponId
      );
    } catch (error) {
      console.error('Error deleting coupon:', error);
      throw new Error('Failed to delete coupon');
    }
  }

  // ========== ORDER MANAGEMENT (Admin) ==========

  // Refund order (admin only)
  async refundOrder(orderId: string): Promise<void> {
    try {
      // Get the order first
      const orderResponse = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId
      );
      
      const order = orderResponse as unknown as Order;
      
      if (order.status !== 'completed') {
        throw new Error('Only completed orders can be refunded');
      }

      // Update order status to refunded
      await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        orderId,
        {
          status: 'refunded',
          refundDate: new Date().toISOString()
        }
      );

      // Return credits to user (this would need to be implemented in userService)
      // For now, we'll create a credit transaction record
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        ID.unique(),
        {
          userId: order.userId,
          type: 'refund',
          amount: order.totalCredits,
          reason: `Refund for order ${order.orderNumber}`,
          relatedOrderId: orderId,
          balanceBefore: 0, // Would need to get current balance
          balanceAfter: order.totalCredits, // Would need to calculate new balance
          metadata: JSON.stringify({
            originalOrderId: orderId,
            refundDate: new Date().toISOString()
          })
        }
      );

    } catch (error) {
      console.error('Error refunding order:', error);
      throw new Error('Failed to refund order');
    }
  }
}

// Export singleton instance
export const storeService = new StoreService();
