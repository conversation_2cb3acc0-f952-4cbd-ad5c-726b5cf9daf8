// Professional Request Manager with Caching and Pre-fetching
import { databases, account } from '@/lib/appwrite/config';
import { AuthUser, UserStats, AdminStats, StorePlan, Order } from '@/types';
import { performanceMonitor } from '@/lib/utils/performance';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface RequestState {
  loading: boolean;
  promise: Promise<any> | null;
}

class RequestManager {
  private static instance: RequestManager;
  private cache = new Map<string, CacheEntry<any>>();
  private requestStates = new Map<string, RequestState>();
  private prefetchQueue = new Set<string>();
  
  // Cache TTL configurations (in milliseconds)
  private readonly cacheTTL = {
    userStats: 30000,      // 30 seconds
    adminStats: 60000,     // 1 minute  
    storePlans: 300000,    // 5 minutes
    userProfile: 120000,   // 2 minutes
    orders: 60000,         // 1 minute
    cartItems: 10000,      // 10 seconds
    userList: 180000,      // 3 minutes
  };

  static getInstance(): RequestManager {
    if (!RequestManager.instance) {
      RequestManager.instance = new RequestManager();
    }
    return RequestManager.instance;
  }

  private constructor() {
    // Set up periodic cache cleanup
    setInterval(() => this.cleanupExpiredCache(), 60000); // Every minute
    
    // Pre-fetch critical data on initialization
    this.initializePrefetch();
  }

  // Generate cache key
  private getCacheKey(type: string, ...params: (string | number)[]): string {
    return `${type}:${params.join(':')}`;
  }

  // Check if cache entry is valid
  private isCacheValid<T>(entry: CacheEntry<T>): boolean {
    return Date.now() < entry.expiresAt;
  }

  // Get from cache or execute function
  async getCached<T>(
    type: keyof typeof this.cacheTTL,
    fetcher: () => Promise<T>,
    ...keyParams: (string | number)[]
  ): Promise<T> {
    const cacheKey = this.getCacheKey(type, ...keyParams);
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      return cached.data as T;
    }

    // Check if request is already in progress
    const requestState = this.requestStates.get(cacheKey);
    if (requestState?.loading && requestState.promise) {
      return requestState.promise as Promise<T>;
    }

    // Execute new request
    const promise = this.executeRequest(fetcher, type, cacheKey);
    this.requestStates.set(cacheKey, { loading: true, promise });

    try {
      const result = await promise;
      this.setCache(cacheKey, result, this.cacheTTL[type]);
      return result;
    } finally {
      this.requestStates.delete(cacheKey);
    }
  }

  // Execute request with error handling and retries
  private async executeRequest<T>(
    fetcher: () => Promise<T>,
    type: string,
    cacheKey: string
  ): Promise<T> {
    const maxRetries = 3;
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const endTiming = performanceMonitor.startTiming(`request:${type}`);
        const result = await fetcher();
        endTiming();
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on authentication errors
        if (error instanceof Error && error.message.includes('401')) {
          throw error;
        }
        
        // Exponential backoff for retries
        if (attempt < maxRetries) {
          await this.delay(Math.pow(2, attempt - 1) * 1000);
        }
      }
    }
    
    throw lastError!;
  }

  // Set cache entry
  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl
    });
  }

  // Pre-fetch critical data
  private async initializePrefetch(): Promise<void> {
    // Only prefetch on client-side to avoid build-time errors
    if (typeof window === 'undefined') return;
    
    // Pre-fetch store plans using public method (most commonly accessed)
    this.prefetch('storePlans', () => this.fetchActiveStorePlans());
  }

  // Add to pre-fetch queue
  prefetch<T>(
    type: keyof typeof this.cacheTTL,
    fetcher: () => Promise<T>,
    ...keyParams: (string | number)[]
  ): void {
    // Skip prefetch during SSR/build time
    if (typeof window === 'undefined') return;
    
    const cacheKey = this.getCacheKey(type, ...keyParams);
    
    if (!this.prefetchQueue.has(cacheKey)) {
      this.prefetchQueue.add(cacheKey);
      
      // Execute pre-fetch in background
      setTimeout(async () => {
        try {
          await this.getCached(type, fetcher, ...keyParams);
        } catch (error) {
          // Only log non-authorization errors to avoid spam
          if (error instanceof Error && !error.message.includes('401') && !error.message.includes('unauthorized')) {
            console.warn(`Pre-fetch failed for ${cacheKey}:`, error);
          }
        } finally {
          this.prefetchQueue.delete(cacheKey);
        }
      }, 100);
    }
  }

  // Invalidate cache
  invalidate(type: string, ...keyParams: (string | number)[]): void {
    const cacheKey = this.getCacheKey(type, ...keyParams);
    this.cache.delete(cacheKey);
  }

  // Invalidate multiple cache entries by pattern
  invalidatePattern(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  // Clean up expired cache entries
  private cleanupExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now >= entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  // Utility delay function
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Optimized data fetching methods
  async fetchUserStats(userId: string): Promise<UserStats> {
    return this.getCached('userStats', async () => {
      const { userService } = await import('@/lib/services/userService');
      return userService.getUserStats(userId);
    }, userId);
  }

  async fetchAdminStats(): Promise<AdminStats> {
    return this.getCached('adminStats', async () => {
      const { adminService } = await import('@/lib/services/adminService');
      return adminService.getAdminStats();
    });
  }

  async fetchStorePlans(): Promise<StorePlan[]> {
    return this.getCached('storePlans', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getAllPlans();
    });
  }

  async fetchActiveStorePlans(): Promise<StorePlan[]> {
    return this.getCached('storePlans', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getActivePlans();
    }, 'active');
  }

  async fetchAdminStorePlans(): Promise<StorePlan[]> {
    return this.getCached('storePlans', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getAllPlans();
    }, 'admin');
  }

  async fetchUserProfile(userId: string): Promise<any> {
    return this.getCached('userProfile', async () => {
      const { userService } = await import('@/lib/services/userService');
      return userService.getUserConfiguration(userId);
    }, userId);
  }

  async fetchUserOrders(userId: string): Promise<Order[]> {
    return this.getCached('orders', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getUserOrders(userId);
    }, userId);
  }

  async fetchCartItems(userId: string): Promise<any[]> {
    return this.getCached('cartItems', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getCartItems(userId);
    }, userId);
  }

  async fetchAllUsers(): Promise<any[]> {
    return this.getCached('userList', async () => {
      const { adminService } = await import('@/lib/services/adminService');
      return adminService.getAllUsers();
    });
  }

  async fetchAllOrders(): Promise<Order[]> {
    return this.getCached('orders', async () => {
      const { storeService } = await import('@/lib/services/storeService');
      return storeService.getAllOrders();
    }, 'all');
  }

  // Pre-load user data when they authenticate
  async preloadUserData(user: AuthUser): Promise<void> {
    // Skip preload during SSR/build time
    if (typeof window === 'undefined') return;
    
    const promises: Promise<any>[] = [];

    // Pre-fetch user stats
    promises.push(this.fetchUserStats(user.$id));
    
    // Pre-fetch user profile
    promises.push(this.fetchUserProfile(user.$id));
    
    // Pre-fetch cart items
    promises.push(this.fetchCartItems(user.$id));
    
    // Pre-fetch store plans based on user role
    if (user.isAdmin) {
      // Admin users can access all plans
      promises.push(this.fetchAdminStorePlans());
    } else {
      // Regular users only need active plans
      promises.push(this.fetchActiveStorePlans());
    }
    
    // Pre-fetch admin data if user is admin
    if (user.isAdmin) {
      promises.push(this.fetchAdminStats());
      promises.push(this.fetchAllUsers());
      promises.push(this.fetchAllOrders());
    }

    // Execute all pre-fetches in parallel (but don't wait for them)
    Promise.allSettled(promises).then((results) => {
      const failed = results.filter(r => r.status === 'rejected').length;
      if (failed === 0) {
        console.log('User data pre-loading completed successfully');
      } else {
        console.log(`User data pre-loading completed with ${failed} failures`);
      }
    });
  }

  // Get cache statistics for debugging
  getCacheStats() {
    const stats = {
      totalEntries: this.cache.size,
      validEntries: 0,
      expiredEntries: 0,
      requestsInProgress: this.requestStates.size,
      prefetchQueueSize: this.prefetchQueue.size
    };

    const now = Date.now();
    for (const entry of this.cache.values()) {
      if (now < entry.expiresAt) {
        stats.validEntries++;
      } else {
        stats.expiredEntries++;
      }
    }

    return stats;
  }

  // Force refresh specific data
  async forceRefresh<T>(
    type: keyof typeof this.cacheTTL,
    fetcher: () => Promise<T>,
    ...keyParams: (string | number)[]
  ): Promise<T> {
    this.invalidate(type, ...keyParams);
    return this.getCached(type, fetcher, ...keyParams);
  }
}

// Export singleton instance
export const requestManager = RequestManager.getInstance();

// Helper hook for React components
export function useRequestManager() {
  return requestManager;
}
