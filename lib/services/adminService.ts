'use client';

import { databases, ID } from '@/lib/appwrite/config';
import { appwriteConfig } from '@/lib/appwrite/config';
import { 
  AdminStats, 
  UserManagement, 
  AuthUser, 
  UserStats, 
  CreditTransaction, 
  Order, 
  AdminLog 
} from '@/types';
import { Query } from 'appwrite';
import { userService } from './userService';

export class AdminService {
  // ========== ADMIN STATS ==========

  // Get comprehensive admin dashboard statistics
  async getAdminStats(): Promise<AdminStats> {
    try {
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();

      // Get actual counts by fetching documents and using the total property
      const [
        totalUsersResponse,
        totalOrdersResponse,
        totalImagesResponse,
        monthlyOrdersResponse,
        monthlyUsersResponse
      ] = await Promise.all([
        // Total users - fetch with reasonable limit to get accurate total
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.userProfilesCollectionId,
          [Query.limit(5000)] // High limit to ensure we get accurate total
        ),
        // Total orders
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.ordersCollectionId,
          [Query.limit(5000)]
        ),
        // Total images
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.uploadedImagesCollectionId,
          [Query.limit(5000)]
        ),
        // Monthly orders
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.ordersCollectionId,
          [
            Query.greaterThanEqual('purchaseDate', firstDayOfMonth),
            Query.limit(5000)
          ]
        ),
        // Monthly new users
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.userProfilesCollectionId,
          [
            Query.greaterThanEqual('$createdAt', firstDayOfMonth),
            Query.limit(5000)
          ]
        )
      ]);

      // Calculate revenue
      const totalRevenue = await this.calculateTotalRevenue();
      const revenueThisMonth = await this.calculateMonthlyRevenue(firstDayOfMonth);

      // Calculate active users (users who logged in in the last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
      const activeUsersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        [
          Query.greaterThanEqual('lastLogin', thirtyDaysAgo),
          Query.limit(5000)
        ]
      );

      return {
        totalUsers: totalUsersResponse.total,
        totalRevenue,
        totalOrders: totalOrdersResponse.total,
        totalImages: totalImagesResponse.total,
        activeUsers: activeUsersResponse.total,
        revenueThisMonth,
        ordersThisMonth: monthlyOrdersResponse.total,
        newUsersThisMonth: monthlyUsersResponse.total
      };
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      throw new Error('Failed to fetch admin statistics');
    }
  }

  // Calculate total revenue from orders
  private async calculateTotalRevenue(): Promise<number> {
    try {
      const ordersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [
          Query.equal('status', 'completed'),
          Query.limit(1000) // Adjust limit as needed
        ]
      );

      return ordersResponse.documents.reduce((total, order: any) => {
        return total + (order.totalCredits || 0);
      }, 0);
    } catch (error) {
      console.error('Error calculating total revenue:', error);
      return 0;
    }
  }

  // Calculate monthly revenue
  private async calculateMonthlyRevenue(startDate: string): Promise<number> {
    try {
      const ordersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [
          Query.equal('status', 'completed'),
          Query.greaterThanEqual('purchaseDate', startDate),
          Query.limit(1000)
        ]
      );

      return ordersResponse.documents.reduce((total, order: any) => {
        return total + (order.totalCredits || 0);
      }, 0);
    } catch (error) {
      console.error('Error calculating monthly revenue:', error);
      return 0;
    }
  }

  // ========== USER MANAGEMENT ==========

  // Get all users with their stats for admin management
  async getAllUsers(limit: number = 50, offset: number = 0): Promise<UserManagement[]> {
    try {
      const usersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        [
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      // First, get all user IDs to fetch names from Auth API
      const userIds = usersResponse.documents.map(doc => doc.$id);

      // Fetch user names from Auth system via API
      let userNames: Record<string, { name: string; email: string }> = {};
      try {
        const response = await fetch('/api/admin/user-names', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userIds }),
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            userNames = result.data;
          }
        }
      } catch (error) {
        console.warn('Could not fetch user names from Auth API:', error);
      }

      const userManagementData = await Promise.all(
        usersResponse.documents.map(async (userDoc: any) => {
          try {
            // Get user stats
            const stats = await userService.getUserStats(userDoc.$id);

            // Get user transactions (last 10)
            const transactions = await this.getUserTransactions(userDoc.$id, 10);

            // Get user orders (last 5)
            const orders = await this.getUserOrders(userDoc.$id, 5);

            // Get user name from Auth API or fallback
            const authUserData = userNames[userDoc.$id];
            const userName = authUserData?.name || userDoc.name || `User_${userDoc.$id.slice(-6)}`;
            const userEmail = authUserData?.email || userDoc.email || '';

            // Create AuthUser object
            const user: AuthUser = {
              $id: userDoc.$id,
              name: userName,
              email: userEmail,
              isAdmin: userDoc.isAdmin || false,
              totalImagesLimit: userDoc.totalImagesLimit || 100,
              credits: userDoc.credits || 0
            };

            return {
              user,
              stats,
              transactions,
              orders
            };
          } catch (error) {
            console.error(`Error fetching data for user ${userDoc.$id}:`, error);
            // Return minimal data if there's an error
            return {
              user: {
                $id: userDoc.$id,
                name: userDoc.name || 'Unknown User',
                email: userDoc.email || '',
                isAdmin: userDoc.isAdmin || false,
                totalImagesLimit: userDoc.totalImagesLimit || 100,
                credits: userDoc.credits || 0
              },
              stats: {
                currentImages: 0,
                totalImagesLimit: userDoc.totalImagesLimit || 100,
                credits: userDoc.credits || 0,
                storageUsed: 0,
                storageLimit: 1000
              },
              transactions: [],
              orders: []
            };
          }
        })
      );

      return userManagementData;
    } catch (error) {
      console.error('Error fetching all users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  // Get user transactions
  private async getUserTransactions(userId: string, limit: number = 10): Promise<CreditTransaction[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit)
        ]
      );

      return response.documents as unknown as CreditTransaction[];
    } catch (error) {
      console.error(`Error fetching transactions for user ${userId}:`, error);
      return [];
    }
  }

  // Get user orders
  private async getUserOrders(userId: string, limit: number = 5): Promise<Order[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.ordersCollectionId,
        [
          Query.equal('userId', userId),
          Query.orderDesc('purchaseDate'),
          Query.limit(limit)
        ]
      );

      return response.documents.map(doc => ({
        ...(doc as unknown as Order),
        items: JSON.parse(doc.items || '[]')
      }));
    } catch (error) {
      console.error(`Error fetching orders for user ${userId}:`, error);
      return [];
    }
  }

  // Update user credits (admin action)
  async updateUserCredits(
    userId: string, 
    newCredits: number, 
    adminUserId: string, 
    reason: string
  ): Promise<void> {
    try {
      // Get current user config
      const currentConfig = await userService.getUserConfiguration(userId);
      const oldCredits = currentConfig.credits;
      const creditChange = newCredits - oldCredits;

      // Update user credits
      await userService.updateUserConfiguration(userId, { credits: newCredits });

      // Log credit transaction
      await this.logCreditTransaction({
        userId,
        type: creditChange > 0 ? 'admin_credit' : 'manual_adjustment',
        amount: creditChange,
        description: reason || 'Admin credit adjustment',
        balanceAfter: newCredits,
        transactionDate: new Date().toISOString()
      });

      // Log admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_credits',
        targetType: 'user',
        targetId: userId,
        details: `Changed credits from ${oldCredits} to ${newCredits}. Reason: ${reason}`,
        success: true
      });
    } catch (error) {
      console.error('Error updating user credits:', error);
      
      // Log failed admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_credits',
        targetType: 'user',
        targetId: userId,
        details: `Failed to change credits. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      });

      throw new Error('Failed to update user credits');
    }
  }

  // Update user image limit (admin action)
  async updateUserImageLimit(
    userId: string, 
    newLimit: number, 
    adminUserId: string, 
    reason: string
  ): Promise<void> {
    try {
      // Get current user config
      const currentConfig = await userService.getUserConfiguration(userId);
      const oldLimit = currentConfig.totalImagesLimit;

      // Update user image limit
      await userService.updateUserConfiguration(userId, { totalImagesLimit: newLimit });

      // Log admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_image_limit',
        targetType: 'user',
        targetId: userId,
        details: `Changed image limit from ${oldLimit} to ${newLimit}. Reason: ${reason}`,
        success: true
      });
    } catch (error) {
      console.error('Error updating user image limit:', error);
      
      // Log failed admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_image_limit',
        targetType: 'user',
        targetId: userId,
        details: `Failed to change image limit. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      });

      throw new Error('Failed to update user image limit');
    }
  }

  // Suspend/unsuspend user account
  async updateUserStatus(
    userId: string, 
    status: 'active' | 'suspended' | 'pending',
    adminUserId: string, 
    reason: string
  ): Promise<void> {
    try {
      // Update user status (assuming we add accountStatus to user profiles)
      await userService.updateUserConfiguration(userId, { 
        accountStatus: status 
      } as any);

      // Log admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_status',
        targetType: 'user',
        targetId: userId,
        details: `Changed user status to ${status}. Reason: ${reason}`,
        success: true
      });
    } catch (error) {
      console.error('Error updating user status:', error);
      
      // Log failed admin action
      await this.logAdminAction({
        adminUserId,
        action: 'update_user_status',
        targetType: 'user',
        targetId: userId,
        details: `Failed to change user status. Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      });

      throw new Error('Failed to update user status');
    }
  }

  // ========== LOGGING SYSTEM ==========

  // Log credit transaction
  async logCreditTransaction(transaction: Omit<CreditTransaction, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        ID.unique(),
        transaction
      );
    } catch (error) {
      console.error('Error logging credit transaction:', error);
      // Don't throw error for logging failures
    }
  }

  // Log admin action
  async logAdminAction(log: Omit<AdminLog, '$id' | '$createdAt' | '$updatedAt'>): Promise<void> {
    try {
      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        ID.unique(),
        log
      );
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Don't throw error for logging failures
    }
  }

  // Get admin logs
  async getAdminLogs(limit: number = 50, offset: number = 0): Promise<AdminLog[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        [
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs:', error);
      throw new Error('Failed to fetch admin logs');
    }
  }

  // Get admin logs for specific admin
  async getAdminLogsByUser(adminUserId: string, limit: number = 50): Promise<AdminLog[]> {
    try {
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.adminLogsCollectionId,
        [
          Query.equal('adminUserId', adminUserId),
          Query.orderDesc('$createdAt'),
          Query.limit(limit)
        ]
      );

      return response.documents as unknown as AdminLog[];
    } catch (error) {
      console.error('Error fetching admin logs by user:', error);
      throw new Error('Failed to fetch admin logs');
    }
  }

  // ========== SEARCH & FILTERS ==========

  // Search users by email or name
  async searchUsers(query: string, limit: number = 20): Promise<UserManagement[]> {
    try {
      // Note: Appwrite's full-text search might be limited
      // This is a basic implementation - you might need to implement more sophisticated search
      const [emailResults, nameResults] = await Promise.all([
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.userProfilesCollectionId,
          [
            Query.search('email', query),
            Query.limit(limit)
          ]
        ).catch(() => ({ documents: [] })), // Fallback if search fails
        databases.listDocuments(
          appwriteConfig.databaseId,
          appwriteConfig.userProfilesCollectionId,
          [
            Query.search('name', query),
            Query.limit(limit)
          ]
        ).catch(() => ({ documents: [] })) // Fallback if search fails
      ]);

      // Combine and deduplicate results
      const allResults = [...emailResults.documents, ...nameResults.documents];
      const uniqueResults = allResults.filter((doc, index, self) => 
        index === self.findIndex(d => d.$id === doc.$id)
      );

      // Convert to UserManagement format (simplified for search results)
      const userManagementData = await Promise.all(
        uniqueResults.slice(0, limit).map(async (userDoc: any) => {
          try {
            const stats = await userService.getUserStats(userDoc.$id);
            
            const user: AuthUser = {
              $id: userDoc.$id,
              name: userDoc.name || 'Unknown User',
              email: userDoc.email || '',
              isAdmin: userDoc.isAdmin || false,
              totalImagesLimit: userDoc.totalImagesLimit || 100,
              credits: userDoc.credits || 0
            };

            return {
              user,
              stats,
              transactions: [], // Don't load transactions for search results
              orders: [] // Don't load orders for search results
            };
          } catch (error) {
            console.error(`Error fetching search data for user ${userDoc.$id}:`, error);
            return null;
          }
        })
      );

      return userManagementData.filter(Boolean) as UserManagement[];
    } catch (error) {
      console.error('Error searching users:', error);
      throw new Error('Failed to search users');
    }
  }

  // Get users by status filter
  async getUsersByStatus(status: 'active' | 'suspended' | 'pending', limit: number = 50): Promise<UserManagement[]> {
    try {
      const usersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        [
          Query.equal('accountStatus', status),
          Query.orderDesc('$createdAt'),
          Query.limit(limit)
        ]
      );

      // Convert to UserManagement format (simplified)
      const userManagementData = await Promise.all(
        usersResponse.documents.map(async (userDoc: any) => {
          try {
            const stats = await userService.getUserStats(userDoc.$id);
            
            const user: AuthUser = {
              $id: userDoc.$id,
              name: userDoc.name || 'Unknown User',
              email: userDoc.email || '',
              isAdmin: userDoc.isAdmin || false,
              totalImagesLimit: userDoc.totalImagesLimit || 100,
              credits: userDoc.credits || 0
            };

            return {
              user,
              stats,
              transactions: [],
              orders: []
            };
          } catch (error) {
            console.error(`Error fetching status filter data for user ${userDoc.$id}:`, error);
            return null;
          }
        })
      );

      return userManagementData.filter(Boolean) as UserManagement[];
    } catch (error) {
      console.error('Error fetching users by status:', error);
      throw new Error('Failed to fetch users by status');
    }
  }

  // Get single user by ID with full management data
  async getUserById(userId: string): Promise<AuthUser> {
    try {
      // Get user document
      const userDoc = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId
      );

      // Get user stats
      const stats = await userService.getUserStats(userId);

      // Create AuthUser object with current stats
      const user: AuthUser = {
        $id: userDoc.$id,
        name: userDoc.name || 'Unknown User',
        email: userDoc.email || '',
        isAdmin: userDoc.isAdmin || false,
        totalImagesLimit: userDoc.totalImagesLimit || 100,
        credits: userDoc.credits || 0,
        // Add current stats
        currentImages: stats.currentImages
      };

      return user;
    } catch (error) {
      console.error(`Error fetching user ${userId}:`, error);
      throw new Error('Failed to fetch user data');
    }
  }

  // Update user information
  async updateUser(userId: string, updates: Partial<AuthUser>): Promise<AuthUser> {
    try {
      // Update user document
      const updatedDoc = await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId,
        {
          name: updates.name,
          email: updates.email,
          isAdmin: updates.isAdmin,
          totalImagesLimit: updates.totalImagesLimit,
          credits: updates.credits
        }
      );

      // Also update user configuration if needed
      if (updates.totalImagesLimit !== undefined || updates.credits !== undefined) {
        await userService.updateUserConfiguration(userId, {
          totalImagesLimit: updates.totalImagesLimit,
          credits: updates.credits
        });
      }

      // Return updated user
      return await this.getUserById(userId);
    } catch (error) {
      console.error(`Error updating user ${userId}:`, error);
      throw new Error('Failed to update user');
    }
  }

  // ========== DAILY CREDITS SYSTEM ==========

  // Check if user can earn daily credits (once per 24 hours)
  async canEarnDailyCredits(userId: string): Promise<boolean> {
    try {
      const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      
      const response = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        [
          Query.equal('userId', userId),
          Query.equal('type', 'daily_earn'),
          Query.greaterThan('$createdAt', twentyFourHoursAgo),
          Query.limit(1)
        ]
      );

      return response.documents.length === 0;
    } catch (error) {
      console.error('Error checking daily credits eligibility:', error);
      return false;
    }
  }

  // Award daily credits to user (30 credits per day)
  async awardDailyCredits(userId: string): Promise<CreditTransaction> {
    try {
      const canEarn = await this.canEarnDailyCredits(userId);
      if (!canEarn) {
        throw new Error('Daily credits already earned in the last 24 hours');
      }

      const dailyAmount = 30;
      
      // Get current user configuration
      const currentConfig = await userService.getUserConfiguration(userId);
      const newCredits = currentConfig.credits + dailyAmount;
      
      // Update user credits
      await userService.updateUserConfiguration(userId, {
        credits: newCredits
      });

      // Create credit transaction record
      const transaction = await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.creditTransactionsCollectionId,
        ID.unique(),
        {
          userId,
          type: 'daily_earn',
          amount: dailyAmount,
          description: 'Daily credits earned',
          balanceAfter: newCredits,
          transactionDate: new Date().toISOString()
        }
      );

      return transaction as unknown as CreditTransaction;
    } catch (error) {
      console.error('Error awarding daily credits:', error);
      throw error;
    }
  }

  // ========== END DAILY CREDITS ==========

  // Fix existing users with missing names (one-time migration)
  async fixUsersWithMissingNames(): Promise<{ updated: number; errors: number }> {
    try {
      console.log('Starting migration to fix users with missing names...');

      // Get all users
      const usersResponse = await databases.listDocuments(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        [Query.limit(1000)]
      );

      let updated = 0;
      let errors = 0;

      for (const userDoc of usersResponse.documents) {
        try {
          // Check if user has missing or empty name
          if (!userDoc.name || userDoc.name === 'Unknown User' || userDoc.name.trim() === '') {
            // Try to get user info from Appwrite account
            try {
              // For now, we'll set a placeholder name based on email
              // In a real scenario, you'd want to get this from Discord API
              const emailName = userDoc.email ? userDoc.email.split('@')[0] : `User_${userDoc.$id.slice(-6)}`;
              const displayName = emailName.charAt(0).toUpperCase() + emailName.slice(1);

              await databases.updateDocument(
                appwriteConfig.databaseId,
                appwriteConfig.userProfilesCollectionId,
                userDoc.$id,
                {
                  name: displayName
                }
              );

              console.log(`Updated user ${userDoc.$id} with name: ${displayName}`);
              updated++;
            } catch (updateError) {
              console.error(`Failed to update user ${userDoc.$id}:`, updateError);
              errors++;
            }
          }
        } catch (error) {
          console.error(`Error processing user ${userDoc.$id}:`, error);
          errors++;
        }
      }

      console.log(`Migration completed: ${updated} users updated, ${errors} errors`);
      return { updated, errors };
    } catch (error) {
      console.error('Error during user name migration:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const adminService = new AdminService();
