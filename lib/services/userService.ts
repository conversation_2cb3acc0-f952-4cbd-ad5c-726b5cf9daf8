import { UserStats, UserConfiguration, DEFAULT_USER_CONFIG } from '@/types';
import { databases, appwriteConfig } from '@/lib/appwrite/config';
import { Query } from 'appwrite';

export class UserService {
  // Get user statistics from Appwrite database
  async getUserStats(userId: string): Promise<UserStats> {
    try {
      // Get user configuration from database
      const userConfig = await this.getUserConfiguration(userId);

      // Get current images count from images collection
      let currentImages = 0;
      try {
        if (appwriteConfig.uploadedImagesCollectionId) {
          const imagesResponse = await databases.listDocuments(
            appwriteConfig.databaseId,
            appwriteConfig.uploadedImagesCollectionId,
            [Query.equal('userId', userId)]
          );
          currentImages = imagesResponse.total;
        }
      } catch (error) {
        console.warn('Could not fetch images count, using 0:', error);
      }

      // Calculate storage used from uploaded images
      let storageUsed = 0;
      try {
        if (appwriteConfig.uploadedImagesCollectionId) {
          const imagesResponse = await databases.listDocuments(
            appwriteConfig.databaseId,
            appwriteConfig.uploadedImagesCollectionId,
            [
              Query.equal('userId', userId),
              Query.limit(1000) // Get up to 1000 images to calculate storage
            ]
          );

          // Sum up file sizes (fileSize is in bytes, convert to MB)
          storageUsed = imagesResponse.documents.reduce((total, image: any) => {
            return total + (image.fileSize || 0);
          }, 0);

          // Convert bytes to MB
          storageUsed = Math.round(storageUsed / (1024 * 1024));
        }
      } catch (error) {
        console.warn('Could not calculate storage usage, using 0:', error);
        storageUsed = 0;
      }

      return {
        currentImages,
        totalImagesLimit: userConfig.totalImagesLimit,
        credits: userConfig.credits,
        storageUsed,
        storageLimit: 1000, // 1GB limit
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  // Update user configuration in Appwrite database
  async updateUserConfiguration(
    userId: string,
    config: Partial<UserConfiguration>
  ): Promise<UserConfiguration> {
    try {
      // Get current configuration
      const currentConfig = await this.getUserConfiguration(userId);
      const updatedConfig = { ...currentConfig, ...config };

      // Update the document in Appwrite
      await databases.updateDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId,
        config
      );

      console.log('Updated user configuration:', updatedConfig);
      return updatedConfig;
    } catch (error) {
      console.error('Error updating user configuration:', error);
      throw new Error('Failed to update user configuration');
    }
  }

  // Get user configuration from Appwrite database
  async getUserConfiguration(userId: string): Promise<UserConfiguration> {
    try {
      // Try to get existing user profile document
      const userDoc = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId
      );

      return {
        isAdmin: userDoc.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
        totalImagesLimit: userDoc.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
        credits: userDoc.credits ?? DEFAULT_USER_CONFIG.credits,
        theme: userDoc.theme ?? 'dark',
        notifications: userDoc.notifications ?? true,
        embedTitle: userDoc.embedTitle ?? 'Image Hosted on AveImgCloud',
        embedFooter: userDoc.embedFooter ?? 'Powered by AveImgCloud',
        embedColor: userDoc.embedColor ?? '#06b6d4',
        lastDailyCreditsClaimDate: userDoc.lastDailyCreditsClaimDate ?? undefined,
      };
    } catch (error: any) {
      console.warn('Database access error for user configuration:', error.message);

      // If user document doesn't exist, try to create it
      if (error.code === 404) {
        try {
          console.log(`User profile not found for ${userId}, creating new configuration`);
          return await this.createUserConfiguration(userId);
        } catch (createError: any) {
          console.warn('Could not create user configuration, using defaults:', createError.message);
          return this.getDefaultConfiguration();
        }
      }

      // If authorization error or other database error, return defaults
      if (error.code === 401 || error.message?.includes('not authorized')) {
        console.warn('User not authorized for database access, using default configuration');
        return this.getDefaultConfiguration();
      }

      console.error('Error fetching user configuration:', error);
      // Return defaults instead of throwing error
      return this.getDefaultConfiguration();
    }
  }

  // Get default configuration without database access
  private getDefaultConfiguration(): UserConfiguration {
    // Try to get from localStorage as fallback
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('userConfig');
      if (stored) {
        try {
          const parsed = JSON.parse(stored);
          return {
            isAdmin: parsed.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
            totalImagesLimit: parsed.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
            credits: parsed.credits ?? DEFAULT_USER_CONFIG.credits,
            theme: parsed.theme ?? 'dark',
            notifications: parsed.notifications ?? true,
            embedTitle: parsed.embedTitle ?? 'Image Hosted on AveImgCloud',
            embedFooter: parsed.embedFooter ?? 'Powered by AveImgCloud',
            embedColor: parsed.embedColor ?? '#06b6d4',
            lastDailyCreditsClaimDate: parsed.lastDailyCreditsClaimDate ?? undefined,
          };
        } catch (error) {
          console.warn('Error parsing stored user config:', error);
        }
      }
    }

    return {
      isAdmin: DEFAULT_USER_CONFIG.isAdmin,
      totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
      credits: DEFAULT_USER_CONFIG.credits,
      theme: 'dark' as const,
      notifications: true,
      embedTitle: 'Image Hosted on AveImgCloud',
      embedFooter: 'Powered by AveImgCloud',
      embedColor: '#06b6d4',
      lastDailyCreditsClaimDate: undefined,
    };
  }

  // Create user configuration with defaults (public method for auth service)
  async createUserConfiguration(userId: string, userInfo?: { name: string; email: string }): Promise<UserConfiguration> {
    try {
      const defaultConfig: UserConfiguration = {
        isAdmin: DEFAULT_USER_CONFIG.isAdmin,
        totalImagesLimit: DEFAULT_USER_CONFIG.totalImagesLimit,
        credits: DEFAULT_USER_CONFIG.credits,
        theme: 'dark' as const,
        notifications: true,
        embedTitle: 'Image Hosted on AveImgCloud',
        embedFooter: 'Powered by AveImgCloud',
        embedColor: '#06b6d4',
        lastDailyCreditsClaimDate: undefined,
      };

      // Include user info if provided
      const documentData = {
        ...defaultConfig,
        ...(userInfo && {
          name: userInfo.name,
          email: userInfo.email
        })
      };

      await databases.createDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId,
        documentData
      );

      console.log(`Created new user profile for user ${userId}${userInfo ? ` (${userInfo.name})` : ''}`);

      // Return the actual configuration that was saved (including name and email)
      return {
        ...defaultConfig,
        ...(userInfo && {
          name: userInfo.name,
          email: userInfo.email
        })
      };
    } catch (error: any) {
      // Handle case where document already exists (error code 409)
      if (error.code === 409 || error.message?.includes('already exists')) {
        console.warn(`User profile already exists for user ${userId}, fetching existing configuration`);

        // Document already exists, fetch and return the existing configuration
        try {
          const existingDoc = await databases.getDocument(
            appwriteConfig.databaseId,
            appwriteConfig.userProfilesCollectionId,
            userId
          );

          return {
            isAdmin: existingDoc.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
            totalImagesLimit: existingDoc.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
            credits: existingDoc.credits ?? DEFAULT_USER_CONFIG.credits,
            theme: existingDoc.theme ?? 'dark',
            notifications: existingDoc.notifications ?? true,
            embedTitle: existingDoc.embedTitle ?? 'Image Hosted on AveImgCloud',
            embedFooter: existingDoc.embedFooter ?? 'Powered by AveImgCloud',
            embedColor: existingDoc.embedColor ?? '#06b6d4',
            lastDailyCreditsClaimDate: existingDoc.lastDailyCreditsClaimDate ?? undefined,
          };
        } catch (fetchError) {
          console.warn('Could not fetch existing user configuration, using defaults:', fetchError);
          return this.getDefaultConfiguration();
        }
      }

      console.error('Error creating user configuration:', error);
      throw new Error('Failed to create user configuration');
    }
  }

  // Ensure user exists in database (called during OAuth)
  async ensureUserExists(userId: string, userInfo?: { name: string; email: string }): Promise<UserConfiguration> {
    try {
      // First, try to get existing configuration directly
      const userDoc = await databases.getDocument(
        appwriteConfig.databaseId,
        appwriteConfig.userProfilesCollectionId,
        userId
      );

      // User exists, update name/email if provided and different
      if (userInfo && (userDoc.name !== userInfo.name || userDoc.email !== userInfo.email)) {
        try {
          await databases.updateDocument(
            appwriteConfig.databaseId,
            appwriteConfig.userProfilesCollectionId,
            userId,
            {
              name: userInfo.name,
              email: userInfo.email
            }
          );
          console.log(`Updated user profile for ${userId} with latest Discord info`);
        } catch (updateError) {
          console.warn('Failed to update user profile with latest info:', updateError);
        }
      }

      // Return their configuration
      return {
        isAdmin: userDoc.isAdmin ?? DEFAULT_USER_CONFIG.isAdmin,
        totalImagesLimit: userDoc.totalImagesLimit ?? DEFAULT_USER_CONFIG.totalImagesLimit,
        credits: userDoc.credits ?? DEFAULT_USER_CONFIG.credits,
        theme: userDoc.theme ?? 'dark',
        notifications: userDoc.notifications ?? true,
        embedTitle: userDoc.embedTitle ?? 'Image Hosted on AveImgCloud',
        embedFooter: userDoc.embedFooter ?? 'Powered by AveImgCloud',
        embedColor: userDoc.embedColor ?? '#06b6d4',
        lastDailyCreditsClaimDate: userDoc.lastDailyCreditsClaimDate ?? undefined,
      };
    } catch (error: any) {
      // If user doesn't exist (404), create with defaults and send welcome email
      if (error.code === 404) {
        console.log(`User profile not found for ${userId}, creating new configuration`);
        const config = await this.createUserConfiguration(userId, userInfo);

        // Email service removed - welcome emails disabled
        if (userInfo) {
          console.log(`Email service disabled - no welcome email sent for new user: ${userInfo.email}`);
        }

        return config;
      }

      // For other errors (authorization, etc.), return defaults
      console.warn(`Could not access user profile for ${userId}, using defaults:`, error.message);
      return this.getDefaultConfiguration();
    }
  }

  // Update user credits in database
  async updateCredits(userId: string, amount: number): Promise<number> {
    try {
      const currentConfig = await this.getUserConfiguration(userId);
      const newCredits = currentConfig.credits + amount;

      // Update credits in database
      await this.updateUserConfiguration(userId, { credits: newCredits });

      console.log(`Updated credits for user ${userId}: ${newCredits}`);
      return newCredits;
    } catch (error) {
      console.error('Error updating credits:', error);
      throw new Error('Failed to update credits');
    }
  }

  // Check if user can upload more images
  async canUploadImage(userId: string): Promise<boolean> {
    try {
      const stats = await this.getUserStats(userId);
      return stats.currentImages < stats.totalImagesLimit;
    } catch (error) {
      console.error('Error checking upload permission:', error);
      return false;
    }
  }

  // Get remaining upload slots
  async getRemainingUploads(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.max(0, stats.totalImagesLimit - stats.currentImages);
    } catch (error) {
      console.error('Error calculating remaining uploads:', error);
      return 0;
    }
  }

  // Calculate storage usage percentage
  async getStorageUsagePercentage(userId: string): Promise<number> {
    try {
      const stats = await this.getUserStats(userId);
      return Math.min(100, (stats.storageUsed / stats.storageLimit) * 100);
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return 0;
    }
  }

  // Deduct credits for an action
  async deductCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const currentConfig = await this.getUserConfiguration(userId);

      if (currentConfig.credits < amount) {
        throw new Error('Insufficient credits');
      }

      const newCredits = currentConfig.credits - amount;

      // Update credits in database
      await this.updateUserConfiguration(userId, { credits: newCredits });

      console.log(`Deducted ${amount} credits for ${reason}. New balance: ${newCredits}`);

      return newCredits;
    } catch (error) {
      console.error('Error deducting credits:', error);
      throw error;
    }
  }

  // Add credits to user account
  async addCredits(userId: string, amount: number, reason: string): Promise<number> {
    try {
      const newCredits = await this.updateCredits(userId, amount);
      console.log(`Added ${amount} credits for ${reason}. New balance: ${newCredits}`);
      
      return newCredits;
    } catch (error) {
      console.error('Error adding credits:', error);
      throw error;
    }
  }

  // Daily credits earning feature - users can earn 30 credits every 24 hours
  async claimDailyCredits(userId: string): Promise<{ 
    success: boolean; 
    credits: number; 
    timeUntilNext?: number; 
    message: string; 
  }> {
    try {
      const userConfig = await this.getUserConfiguration(userId);
      const now = new Date();
      const lastClaim = userConfig.lastDailyCreditsClaimDate ? new Date(userConfig.lastDailyCreditsClaimDate) : null;
      
      // Check if 24 hours have passed since last claim
      if (lastClaim) {
        const timeDiff = now.getTime() - lastClaim.getTime();
        const hoursDiff = timeDiff / (1000 * 60 * 60);
        
        if (hoursDiff < 24) {
          const timeUntilNext = Math.ceil((24 - hoursDiff) * 60 * 60 * 1000); // milliseconds
          return {
            success: false,
            credits: 0,
            timeUntilNext,
            message: `You can claim your next daily credits in ${Math.ceil(24 - hoursDiff)} hours.`
          };
        }
      }
      
      // Award 30 daily credits
      const dailyCreditsAmount = 30;
      const newCredits = userConfig.credits + dailyCreditsAmount;
      
      await this.updateUserConfiguration(userId, {
        credits: newCredits,
        lastDailyCreditsClaimDate: now.toISOString()
      });

      // Email service removed - credit transaction notifications disabled
      console.log('Email service disabled - no daily credits notification sent');

      return {
        success: true,
        credits: dailyCreditsAmount,
        message: `You earned ${dailyCreditsAmount} daily credits! Come back tomorrow for more.`
      };
    } catch (error) {
      console.error('Error claiming daily credits:', error);
      return {
        success: false,
        credits: 0,
        message: 'Failed to claim daily credits. Please try again.'
      };
    }
  }

  // Check if user can claim daily credits
  async canClaimDailyCredits(userId: string): Promise<{
    canClaim: boolean;
    timeUntilNext?: number;
  }> {
    try {
      const userConfig = await this.getUserConfiguration(userId);
      const now = new Date();
      const lastClaim = userConfig.lastDailyCreditsClaimDate ? new Date(userConfig.lastDailyCreditsClaimDate) : null;
      
      if (!lastClaim) {
        return { canClaim: true };
      }
      
      const timeDiff = now.getTime() - lastClaim.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);
      
      if (hoursDiff >= 24) {
        return { canClaim: true };
      }
      
      const timeUntilNext = Math.ceil((24 - hoursDiff) * 60 * 60 * 1000);
      return { canClaim: false, timeUntilNext };
    } catch (error) {
      console.error('Error checking daily credits eligibility:', error);
      return { canClaim: false };
    }
  }

  // ========== EXISTING METHODS ==========
}

// Export singleton instance
export const userService = new UserService();
